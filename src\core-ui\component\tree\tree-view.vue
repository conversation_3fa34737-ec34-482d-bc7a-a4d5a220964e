<template>
    <div class="d-flex flex-column h-100">
        <div
            class="d-flex-item-center search-action"
            v-if="types || resetActionLabel"
        >
            <ui-input
                v-model="searchKey"
                placeholder="输入关键字搜索"
                @keydown.native.enter="onSearch"
                :clearable="true"
                @clear="onSearch"
                v-if="types"
            ></ui-input>
            <ui-button
                v-if="resetActionLabel && hasSelected"
                @click="$emit('reset')"
                type="text-primary"
                >{{ resetActionLabel }}</ui-button
            >
        </div>
        <div class="tree-title" :class="{ empty: !treeTitle }">
            {{ treeTitle }}
        </div>
        <el-tree
            class="flex-fill"
            :class="{ auto: auto }"
            v-if="searchTreeData"
            node-key="id"
            :data="searchTreeData"
            :props="treeProps"
            :show-checkbox="enableMulti"
            :check-strictly="enableMulti"
            :default-expand-all="true"
            @check="handleCheck"
            @node-click="handleNodeClick"
            check-on-click-node
            :expand-on-click-node="false"
            highlight-current
            accordion
        >
            <span class="custom-tree-node" slot-scope="{ node, data }">
                <span>{{ node.label }}</span>
                <span v-if="data.relationDataCount">
                    共计 {{ data.relationDataCount }}
                </span>
            </span>
        </el-tree>
        <el-tree
            v-if="!reseting"
            class="default-tree flex-fill"
            :class="{ auto: auto }"
            v-show="!searchTreeData"
            ref="tree"
            node-key="id"
            :props="treeProps"
            :show-checkbox="enableMulti"
            :check-strictly="enableMulti"
            :load="loadLazy"
            @check="handleCheck"
            @node-click="handleNodeClick"
            check-on-click-node
            :expand-on-click-node="false"
            highlight-current
            :default-expanded-keys="defaultExpandKeys"
            :default-checked-keys="selectedKeys"
            :current-node-key="curNodeKey"
            accordion
            lazy
        >
            <span
                v-if="openMenu()"
                class="custom-tree-node"
                slot-scope="{ node, data }"
                @contextmenu.prevent="openMenu(node, $event)"
            >
                <span class="u-flex">
                    <img
                        v-if="computeIcon(node)"
                        width="16px"
                        height="16px"
                        :src="computeIcon(node)"
                        class="u-m-r-10"
                    />
                    <span :title="node.label">{{ node.label }}</span>
                </span>
                <span v-if="data.relationDataCount">
                    共计 {{ data.relationDataCount }}
                </span>
            </span>
            <span v-else class="custom-tree-node" slot-scope="{ node, data }">
                <span class="u-flex">
                    <img
                        v-if="computeIcon(node)"
                        width="16px"
                        height="16px"
                        :src="computeIcon(node)"
                        class="u-m-r-10"
                    />
                    <span :title="node.label">{{ node.label }}</span>
                </span>
                <span v-if="data.relationDataCount">
                    共计 {{ data.relationDataCount }}
                </span>
            </span>
        </el-tree>
    </div>
</template>

<script lang="ts">
    import { Prop, Component, Vue, Ref, Model, Watch } from "vue-property-decorator"
    import type { ListTypes, TreeTypes } from "uniplat-sdk"
    import { ElTree } from "element-ui/types/tree"
    import { Tree } from "uniplat-sdk/build/main/model/tree/tree"

    @Component({ name: "TreeView" })
    export default class TreeView extends Vue {
        private selectedTreeNode: any[] = []
        private syncedSelectedList: any[] = []
        private readonly treeProps = {
            label: "display",
            children: "children",
            isLeaf: this.isLeaf,
        }

        private treeApi!: Tree
        private treeList: TreeTypes.Node[] = []
        @Prop(String) modelName!: string
        @Prop() rootNode!: number | string
        @Prop({ type: Boolean, default: false }) enableMulti!: boolean
        // @Prop(Object) summary
        @Prop({ type: Array, default: () => [] }) selectedKeys!: string[]
        @Prop({ type: Array, default: () => [] }) defaultExpandKeys!: any[]
        @Prop({ type: Array, default: () => [] }) prefilters!: []
        @Prop({ default: "" }) curNodeKey!: any

        @Prop()
        private readonly auto!: boolean
        @Prop()
        private readonly treeTitle!: string

        private reseting = false

        @Model()
        private readonly value!: ListTypes.getListDataRequestResult["meta"]["tree"]

        @Prop()
        private readonly resetActionLabel!: string

        @Prop({ default: () => null })
        private treeConfig!: any

        @Prop()
        private readonly domainService!: (
            parent?: string | number
        ) => Promise<TreeTypes.Node[]>

        @Prop()
        private readonly types!: string[]

        @Prop({ default: false })
        private readonly directUseRootNode!: boolean

        @Ref()
        private readonly tree!: ElTree<string, any>

        private searchKey = ""

        private searchTreeData: any[] | null = null

        private get hasSelected() {
            return this.selectedTreeNode.length
        }

        private get queryPrefilters() {
            if (this.value && this.value.prefilters) {
                return this.value.prefilters as any[]
            }
            return []
        }

        private get rootParent() {
            if (this.value && this.value.treeRootNodeValue) {
                return this.value.treeRootNodeValue as string
            }
            return ""
        }

        @Watch("prefilters")
        private onPrefiltersChanged(n: any[], o: any[]) {
            let changed = false
            if (n.length !== o.length) {
                changed = true
            } else if (n.length === o.length) {
                for (let i = 0; i < o.length; i++) {
                    if (n[i].value !== o[i].value) {
                        changed = true
                        break
                    }
                }
            } else {
                changed = true
            }
            if (!changed) {
                return
            }
            this.reset()
        }

        private resetTree() {
            this.reseting = true
            this.$nextTick(() => (this.reseting = false))
        }

        private handleNodeClick(obj: any, node: any) {
            if (!this.enableMulti) {
                this.selectedTreeNode = []
                this.selectedTreeNode.push(node)
                this.syncedSelectedList = []
                this.syncedSelectedList.push(obj)
                this.selectedChange()
            }
        }

        private isLeaf(data: any) {
            return !data.hasChildren
        }

        private handleCheck(_: any, data: any) {
            if (this.enableMulti) {
                this.syncedSelectedList = data.checkedNodes
                this.selectedChange()
            }
        }

        private selectedChange() {
            this.$emit(
                "node-select-change",
                this.syncedSelectedList,
                this.selectedTreeNode
            )
        }

        private init() {
            if (this.modelName) {
                this.treeApi = new Tree(this.modelName)
            }
            this.syncedSelectedList = []
        }

        private async loadLazy(node: any, resolve: any) {
            let parent
            // debugger
            // 如果外部传递了根，则优先查询外部传递的根

            if (this.rootNode && node.level === 0 && !this.domainService) {
                const rootTreeNode = await new Tree(this.modelName).getNode(
                    this.rootNode,
                    false
                )
                if (!rootTreeNode) {
                    console.error(`${this.rootNode} 查询不到节点，请检查配置`)
                }
                // 默认展开 treeRootNode
                if (!this.defaultExpandKeys.includes(this.rootNode)) {
                    this.defaultExpandKeys.push(this.rootNode)
                }
                resolve(this.handleRes([rootTreeNode]))
                if (this.directUseRootNode) {
                    this.$nextTick(() => {
                        this.syncedSelectedList = [rootTreeNode]
                        this.selectedTreeNode = [this.tree.getNode(this.rootNode)]
                        this.tree.setCurrentKey(this.rootNode + "")
                        this.$emit(
                            "set-default-value",
                            this.syncedSelectedList,
                            this.selectedTreeNode
                        )
                    })
                }
            } else {
                if (node && node.data !== undefined) {
                    parent = node.data.id
                }
                if (this.treeApi === undefined) {
                    this.init()
                }
                const res = await this.loadTreeLazy(parent, node)
                if (this.rootNode && node.level === 0 && !!this.domainService) {
                    setTimeout(() => {
                        if (this.tree) {
                            if (this.rootNode) {
                                const node = this.tree.getNode(this.rootNode)
                                this.tree.setCheckedKeys([])
                                return node && this.tree.setCurrentNode(node.data)
                            }
                        }
                    })
                }
                // auto expand root when only one child
                if (
                    res.length === 1 &&
                    (res[0].data as { depth: number }).depth <= 0 &&
                    node.level === 0
                ) {
                    setTimeout(
                        () =>
                            this.$nextTick(() => {
                                const e = this.$el.querySelector(
                                    ".el-tree-node__expand-icon"
                                ) as HTMLElement
                                e && e.click()
                            }),
                        300
                    )
                }
                resolve(this.handleRes(res))
            }
        }

        private handleRes(res: any) {
            if (this.treeConfig?.handleRes) {
                this.treeConfig.handleRes(res)
            }
            return res.map((e: any) => {
                let disabled = false
                if (this.treeConfig?.disabled) {
                    disabled = this.treeConfig.disabled(e)
                }
                return { ...e, disabled }
            })
        }

        private loadTreeLazy(parent: string | number, node?: any) {
            if (this.domainService) {
                const p = node?.data?.data?.region_code || parent
                return this.domainService(p)
            }
            return this.treeApi.queryTreeLazy(parent, this.prefilters)
        }

        /**
         * 统计当前节点和它的子节点的
         */
        private summaryTotal(data: TreeTypes.Node) {
            let count = data.relationDataCount == null ? 0 : data.relationDataCount
            data.children.forEach((child) => (count += this.summaryTotal(child)))
            data.relationDataCount = count
            return count
        }

        async mounted() {
            this.init()
            if (this.enableMulti && this.isNotNullOrEmpty(this.selectedKeys)) {
                return this.selectedKeys.forEach((id) =>
                    this.syncedSelectedList.push({ id })
                )
            }
            await this.$nextTick()
            this.setDefaultSelected()
        }

        private setDefaultSelected() {
            if (
                !this.enableMulti &&
                this.selectedKeys &&
                this.selectedKeys.length
            ) {
                const node: any = this.getRef().getNode(this.selectedKeys[0]) as any
                if (node) {
                    this.syncedSelectedList = []
                    this.syncedSelectedList.push(node.data as any)
                    this.selectedChange()
                }
            }
        }

        private isNotNullOrEmpty(items?: any) {
            return !!(items && items.length)
        }

        public async reload(root?: string | number) {
            if (!root) {
                return
            }
            const res = await this.loadTreeLazy(root)
            res.forEach((tree) => this.summaryTotal(tree))
            this.treeList = res
        }

        public getRef() {
            return this.$refs.tree as ElTree<{}, {}>
        }

        public reset() {
            this.searchKey = ""
            this.searchTreeData = null
            if (this.tree) {
                if (this.rootNode) {
                    // const node = this.tree.getNode(this.rootNode)
                    // console.log(node)
                    // this.tree.setCheckedKeys([])
                    // return node && this.tree.setCurrentNode(node.data)
                    this.selectedTreeNode = []
                    this.syncedSelectedList = []
                    this.resetTree()
                    return
                }
            }
            this.selectedTreeNode = []
            this.syncedSelectedList = []
            this.selectedChange()
            this.resetTree()
        }

        private onSearch() {
            if (this.searchKey) {
                this.treeApi
                    .queryAllTreeList({
                        searchKeyword: this.searchKey,
                        searchProperties: this.types,
                        prefilters: this.queryPrefilters,
                        parent: this.rootParent,
                    })
                    .then((r) => {
                        this.searchTreeData = r
                    })
            } else {
                this.searchTreeData = null
            }
        }

        public resetSelected() {
            ;(this.tree.store as any).setCurrentNodeKey(null)
        }

        computeIcon(node: any) {
            if (this.treeConfig?.computeIcon) {
                return this.treeConfig?.computeIcon(node, this.selectedTreeNode)
            }
            return ""
        }

        openMenu(node: any, e: any) {
            if (this.treeConfig?.openMenu) {
                if (!node) return true
                return this.treeConfig?.openMenu(node, e)
            }
            return ""
        }
    }
</script>

<style scoped lang="less">
    @main-color: var(--main-color);
    @text-danger: #dc3545;
    @text-success: #22bd7a;
    @text-warn: #e87005;
    @text-body: #222;
    @text-label: #666;
    @text-hint: #999;
    @primary-border-color: var(--main-color);

    @root-font-family: "PingFangSC-Regular", "PingFang SC", -apple-system,
        BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans",
        sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol",
        "Noto Color Emoji";

    @root-weight-family: "PingFang SC Medium", "PingFangSC-Medium", "PingFang SC",
        -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue",
        Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
        "Segoe UI Symbol", "Noto Color Emoji";

    .el-tree {
        background-color: #fff;
        color: #333;
        overflow: auto;
        max-height: calc(50vh - 10px);
        padding-left: 15px;
        padding-right: 20px;

        &.auto {
            max-height: unset;
        }

        /deep/ .el-tree-node__content {
            height: auto;
        }

        /deep/ .el-tree-node.is-current > .el-tree-node__content {
            font-weight: 600;
            background-color: #edf2fd;
            color: #5782ec;
            font-family: @root-weight-family;
        }
    }

    .tree-title {
        padding: 10px 20px;
        padding-top: 20px;
        font-weight: 600;
        font-size: 16px;
        color: #000000;
        line-height: 16px;

        &.empty {
            padding-top: 10px;
        }
    }

    .custom-tree-node {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
        padding: 8px;
        padding-left: 0;
        line-height: 1.2;
    }

    .search-action {
        padding: 10px;
        background-color: #f5f7fa;

        .el-input + button {
            margin-left: 5px;
        }
    }
</style>
