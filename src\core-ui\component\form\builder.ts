import { isVueComponent } from "@/core-ui/helpers/tools"
import { U<PERSON>ore } from "@/core-ui/service/setup"
import { cloneDeep, find } from "lodash"
import { Action, ActionTypes, ListTypes } from "uniplat-sdk"
import {
    BuildFormConfig,
    FormItem,
    FormRule,
    FormType,
    getDefaultValueForm,
    FileType,
    typeMapping,
} from "."
import { HumbleFilter, ListRow } from "uniplat-sdk/build/main/def"
import { TableFilter } from "../table"

export async function buildFormSections(params: BuildFormConfig) {
    params = _.cloneDeep(params)
    let inputsParameters: ActionTypes.info | undefined
    let action: Action | null = null
    if (params.action || (params.sdkModel && params.sdkAction)) {
        action =
            params.action ||
            UICore.core.model(params.sdkModel!).action(params.sdkAction!)
        if (params.initialParams) {
            action.updateInitialParams(params.initialParams)
        }
        inputsParameters = await action.query(
            params.select_list ||
                (params.id ? [{ v: 0, id: params.id || 0 }] : undefined),
            params.prefilters || undefined,
            params.others
        )
    }
    if (!params.forms.length) {
        params.forms = formConfigGenerator(
            inputsParameters,
            params.requiredConfig
        )
    }
    if (params.sortFormItem) {
        const forms = params.sortFormItem(params.forms)
        if (forms) {
            params.forms = forms
        }
    }
    buildFormItems(params.forms, inputsParameters)
    return _.assign(
        {
            forms: params.forms,
            uniplatVersion: inputsParameters?.uniplat_version || 0,
            data: getDefaultValueForm(params.forms!),
            action: action!,
        },
        params.needSourceData ? { inputsParameters } : null
    )
}

export function formConfigGenerator<T extends FormItem>(
    inputsParameters?: ActionTypes.info,
    requiredConfig?: string[]
) {
    return _.map(inputsParameters?.parameters.inputs_parameters, (item) => {
        let mappingType = item.type
        if (item.type === "cascader" && item.multi) {
            mappingType = "multipleCascader"
        }
        const defaultConfig = item.type
            ? cloneDeep(typeMapping[mappingType])
            : {
                  type: FormType.None,
                  itemStyle: {
                      fontSize: "16px",
                      lineHeight: "1.5",
                  },
              }

        if (item.type === "intentSearch") {
            Object.assign(defaultConfig, {
                sourceInputsParameter: item,
            })
        }

        if (!defaultConfig) {
            return
        }
        const newForm = {
            ...defaultConfig,
            label: item.label,
            prop: item.property,
        } as T

        if (requiredConfig && requiredConfig.includes(item.property)) {
            Object.assign(newForm, {
                required: true,
            })
        }

        if (item.ext_properties?.span) {
            newForm.col = {
                span: item.ext_properties.span,
            }
        }
        if (item.ext_properties?.append) {
            ;(newForm.option! as any).append = item.ext_properties?.append
        }
        return newForm
    }).filter((i) => i) as T[]
}

export function tableFilterConfigGenerator(
    info: ListTypes.IQueryResult<ListRow>
) {
    const extraInfo = (x: any) => {
        const isRangeType = x.rangeType === "range"
        const typeMapping: Record<string, Partial<TableFilter>> = {
            text: {
                type: FormType.Text,
                keyValueFilter: {
                    match: x.match,
                },
            },
            combo_text: {
                type: FormType.Text,
                keyValueFilter: {
                    match: x.match,
                },
            },
            enum: {
                type: FormType.Select,
                option: {
                    filterable: true,
                    multiple: true,
                },
            },
            boolean: {
                type: FormType.Select,
                sourceInputsParameter: buildSelectSource([
                    {
                        key: "1",
                        value: "是",
                    },
                    {
                        key: "0",
                        value: "否",
                    },
                ]),
            },
            number: {
                type: isRangeType ? FormType.TextRange : FormType.InputNumber,
                option: {
                    type: "number",
                },
            },
            date: {
                type: FormType.DatePicker,
                option: {
                    type: "daterange",
                },
            },
            datetime: {
                type: FormType.DatePicker,
                option: {
                    type: "datetimerange",
                },
            },
            cascader: {
                type: x.treeMulti
                    ? FormType.MultipleCascader
                    : FormType.Cascader,
                option: {
                    filterable: true,
                    elProps: (() => {
                        let extraObj = {}
                        const isLazy = !!x?.ext_properties?.mapping?.is_lazy
                        if (isLazy) {
                            const model_name =
                                x.ext_properties.mapping.model_name
                            const name = x.ext_properties.mapping.name
                            const lazyLoad = (node: any, resolve: any) => {
                                UICore.core
                                    .model(model_name)
                                    .mappingFetch({
                                        actionId: "",
                                        form_params: {},
                                        mappingName: name,
                                        selected_list: [],
                                        nodeValue: node.value || "",
                                    })
                                    .then((nodes: any) => {
                                        nodes = nodes.map((n: any) => ({
                                            key: n.key,
                                            value: n.value,
                                            label: n.value,
                                            leaf: n.leaf,
                                        }))
                                        resolve(nodes)
                                    })
                            }

                            Object.assign(extraObj, {
                                lazyLoad: lazyLoad,
                            })
                        }
                        return {
                            checkStrictly: true,
                            multiple: x.treeMulti,
                            lazy: isLazy,
                            ...extraObj,
                        }
                    })(),
                },
            },
            intentSearch: {
                type: FormType.IntentSearchRemote,
                option: {
                    multiple: isRangeType,
                },
            },
            multi_file: {
                type: FormType.MyUpload,
            },
            tree: {
                type: FormType.Tree2,
                option: {
                    setDefaultValue2RootCode: true,
                    treeMulti: x.treeMulti,
                },
            },
        }
        return typeMapping[x.type]
    }
    const tableFilter = info.pageData.meta.filters.map((i) => {
        const r = extraInfo(i)
        if (!r) {
            return null
        }
        let prop = (i as HumbleFilter).property
        if (typeof (i as HumbleFilter).full_property === "string") {
            prop = (i as HumbleFilter).full_property
        }
        return {
            prop,
            label: i.label,
            ...r,
        }
    }) as TableFilter[]

    return tableFilter.filter((i) => i)
}

export function buildFormItems<T extends FormItem>(
    forms: T[],
    inputsParameters?: ActionTypes.info
) {
    return _.map(forms, (form) => {
        return buildFormItem(form, inputsParameters)
    })
}

export function buildFormItem<T extends FormItem>(
    form: T,
    inputsParameters?: ActionTypes.info
) {
    let sourceInputsParameter = _.find(
        inputsParameters?.parameters.inputs_parameters,
        { property: form.useOtherProp || form.prop }
    )
    if (!sourceInputsParameter && form.useTag) {
        // 获取标签group
        const currentTag = inputsParameters?.tagGroups.tagGroups.find(
            (i) => i.tagGroupName === form.useTag
        )
        const d =
            currentTag?.tags.map((i) => ({
                key: i.tagName,
                value: i.tagName,
            })) || []
        // 标签默认值
        const defaultValue = inputsParameters?.tagGroups.tags
            .filter((i) => i.tagGroupName === form.useTag)
            .map((i) => i.tagName)
            .join(",")
        sourceInputsParameter = buildSelectSource(d, currentTag, defaultValue)
    }
    if (!form.sourceInputsParameter) {
        form.sourceInputsParameter = sourceInputsParameter
    } else {
        form.sourceInputsParameter = {
            ...sourceInputsParameter,
            ...form.sourceInputsParameter,
            default_value:
                sourceInputsParameter?.default_value ||
                (form.sourceInputsParameter as ActionTypes.inputsParameter)
                    ?.default_value,
        } as any
    }
    form.emptyDisplay ?? (form.emptyDisplay = "暂无")
    form.option = _.assign({}, form.option, {
        disabled:
            form.option?.disabled ??
            (form.sourceInputsParameter as ActionTypes.inputsParameter)
                ?.readonly ??
            false,
        placeholder:
            form.option?.placeholder ?? getPlaceholderByFormType(form.type),
    })
    if (form.sourceInputsParameter?.type === "hidden") {
        form.hide = true
    }
    if (!form.disabledUniplatRule) {
        form.rules = [
            ...((inputsParameters?.rules[form.prop] || []) as FormRule[]),
            ...(form.rules || []),
        ].map((item) => {
            if (item.required && !item.message) {
                item.message = getPlaceholderByFormType(form.type) + form.label
            }
            return item
        })
    }

    if (form.required && !find(form.rules, { required: true })) {
        const rules = form.rules || []
        rules.push({
            required: true,
            trigger: "blur",
            message: getPlaceholderByFormType(form.type) + form.label,
        })
        form.rules = rules
    }
    if (find(form.rules, { required: true })) {
        delete form.required
    }
    return form
}

export function getPlaceholderByFormType(type: FormItem["type"]) {
    if (!type || isVueComponent(type) || type === FormType.File) {
        return ""
    }
    if (type <= FormType.TextRange) {
        return "请输入"
    }
    return "请选择"
}

export function buildSelectSource(
    mapping: {
        key: string | number
        value: any
    }[],
    extendData?: any,
    default_value?: any
) {
    return {
        ext_properties: {
            mapping: {
                mapping_values: mapping,
            },
            extendData,
        },
        default_value,
    } as ActionTypes.inputsParameter
}
