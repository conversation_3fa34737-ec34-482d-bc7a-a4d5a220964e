import { TableColumn } from "@/core-ui/component/table"

export const auditColumns = (showFilter = false) => {
    const d: TableColumn[] = [
        {
            label: "基本信息",
            prop: "name",
            showOverflowTip: true,
            render: (h, row) => {
                return h("div", [h("div", row.name), h("div", row.mobile_hide)])
            },
        },
        {
            label: "户籍地",
            prop: "户籍地",
            showOverflowTip: true,
            render: (h, row) => {
                return (
                    row.household_province_code_remark +
                    row.household_city_code_remark +
                    row.household_town_code_remark +
                    row.household_village_code_remark
                )
            },
        },
        {
            label: "核验信息",
            prop: "核验信息",
            showOverflowTip: true,
            render: (h, row) => {
                return h("div", [
                    h("div", row.education_degree_remark),
                    h("div", row.employment_type_remark),
                    h(
                        "div",
                        row.work_province_code_remark +
                            row.work_city_code_remark +
                            row.work_district_code_remark
                    ),
                ])
            },
        },
        {
            label: "最近外呼时间",
            prop: "最近外呼时间",
            showOverflowTip: true,
            render: (h, row) => {
                return row["data_verify_result.call_time_label"]
            },
        },
        {
            label: "最近外呼状态",
            prop: "最近外呼状态",
            showOverflowTip: true,
            render: (h, row) => {
                return row["data_verify_result.call_status_remark"]
            },
        },
        {
            label: "外呼次数",
            prop: "外呼次数",
            showOverflowTip: true,
            render: (h, row) => {
                return row["data_verify_result.call_count_label"]
            },
        },
        {
            label: "核验状态",
            prop: "核验状态",
            showOverflowTip: true,
            render: (h, row) => {
                return row["data_verify_result.verify_status_remark"]
            },
        },
        {
            label: "核验标签",
            prop: "核验标签",
            showOverflowTip: true,
            render: (h, row) => {
                return row["data_verify_result.education_degree_tag"]
            },
        },
        {
            label: "操作",
            prop: "h",
            fixed: "right",
            width: "100px",
        },
    ]
    return d
}

export const callOutColumns = (showFilter = false) => {
    const d: TableColumn[] = [
        {
            label: "基本信息",
            prop: "name",
            width: "180px",
            showOverflowTip: true,
            render: (h, row) => {
                return h("div", [h("div", row.name), h("div", row.mobile_hide)])
            },
        },
        {
            label: "任务起止时间",
            prop: "任务起止时间",
            showOverflowTip: true,
            render: (h, row) => {
                return row.begin_time_label + " - " + row.end_time_label
            },
        },
        {
            label: "外呼时间段",
            prop: "外呼时间段",
            showOverflowTip: true,
            render: (h, row) => {
                return h("div", [
                    h("div", row.am_range_label),
                    h("div", row.pm_range_label),
                ])
            },
        },
        {
            label: "状态",
            prop: "状态",
            showOverflowTip: true,
            render: (h, row) => {
                return row.status_label
            },
        },
        {
            label: "外呼成功/外呼失败/待同步/异常数据/全部",
            prop: "外呼成功/外呼失败/待同步/异常数据/全部",
            showOverflowTip: true,
            render: (h, row) => {
                return h("div", [
                    h(
                        "span",
                        { style: { color: "green" } },
                        row.callout_success_count_label
                    ),
                    h("span", "/"),
                    h(
                        "span",
                        { style: { color: "red" } },
                        row.callout_fail_count_label
                    ),
                    h("span", "/"),
                    h(
                        "span",
                        { style: { color: "blue" } },
                        row.callout_wait_sync_count_label
                    ),
                    h("span", "/"),
                    h(
                        "span",
                        { style: { color: "red" } },
                        row.submit_fail_count_label
                    ),
                    h("span", "/"),
                    h("span", row.callout_total_count_label),
                ])
            },
        },
        {
            label: "进度",
            prop: "进度",
            showOverflowTip: true,
            render: (h, row) => {
                return row.schedule_label
            },
        },
        {
            label: "最近同步时间",
            prop: "最近同步时间",
            showOverflowTip: true,
            render: (h, row) => {
                return row.final_change_time_label
            },
        },
        {
            label: `创建人/创建时间`,
            prop: "创建人",
            showOverflowTip: true,
            render: (h, row) => {
                return h("div", [
                    h("div", row["system_user_create_by.info.name"]),
                    h("div", row.create_time_label),
                ])
            },
        },
        {
            label: "操作",
            prop: "callOutH",
            fixed: "right",
            width: "100px",
        },
    ]
    return d
}
