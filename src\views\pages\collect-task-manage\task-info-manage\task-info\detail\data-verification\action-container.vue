<template>
    <el-dialog
        class="import-dialog"
        append-to-body
        :visible="value"
        @close="close"
        :close-on-click-modal="false"
        :width="width"
        :title="action && action.label"
    >
        <form-builder ref="formBuilder" />

        <div class="u-flex u-m-t-50 u-row-center">
            <el-button type="primary" @click="close" plain> 取消 </el-button>
            <el-button
                type="primary"
                class="u-m-l-30"
                :loading="loading"
                @click="confirm"
            >
                确定
            </el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts">
    import {
        buildFormSections,
        FormController,
        FormType,
    } from "@/core-ui/component/form"
    import FormBuilder from "@/core-ui/component/form/index.vue"
    import { DialogController } from "@/core-ui/controller/dialog-controller"
    import { Action, action } from "uniplat-sdk"
    import { Component, Mixins, Prop } from "vue-property-decorator"

    @Component({ components: { FormBuilder } })
    export default class ActionContainer extends Mixins(
        DialogController,
        FormController
    ) {
        private width = "600px"

        @Prop()
        private action!: action

        @Prop()
        private modelName!: string

        @Prop()
        private selected_list!: { id: number; v: number }[]

        @Prop()
        private prefilters!: { property: string; value: string }[]

        @Prop({ default: () => ({}) })
        private filters!: Record<string, any[]>

        private actionData!: Action

        private confirm() {
            const data = this.getFormValues()
            if (Object.keys(data).length === 0) {
                return this.submit(data)
            }
            this.validateForm((v: boolean) => {
                if (v) {
                    this.submit(data)
                }
            })
        }

        private submit(data: any) {
            console.log("data", data)
            this.loading = true
            this.actionData
                .updateInitialParams({
                    selected_list: this.selected_list,
                    prefilters: this.prefilters,
                })
                .addInputs_parameter(data)
                .execute({
                    filters: this.filters,
                })
                .then((res) => {
                    this.$emit("success", res)
                    this.close()
                })
                .finally(() => {
                    this.loading = false
                })
        }

        protected onOpen() {
            this.width = this.action.size_percent
                ? this.action.size_percent + "%"
                : "600px"
            buildFormSections({
                sdkModel: this.modelName,
                sdkAction: this.action.action_name,
                prefilters: this.prefilters,
                select_list: this.selected_list,
                forms: [],
            }).then((r) => {
                this.actionData = r.action
                this.buildForm(r.forms)
            })
        }
    }
</script>

<style lang="less" scoped>
    .import-dialog {
        ::v-deep .el-dialog__body {
            padding-top: 20px;
        }
    }
</style>
