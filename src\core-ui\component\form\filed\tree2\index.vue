<template>
    <div>
        <div class="cursor-pointer" @click="clickInput">
            <el-input
                :value="display"
                readonly="readonly"
                :placeholder="placeholder"
                :disabled="$attrs.disabled"
            >
                <el-button
                    v-if="display"
                    slot="append"
                    icon="el-icon-circle-close"
                    class="clear-btn"
                    @click.stop="clickBtn"
                ></el-button>
            </el-input>
        </div>
        <dialog-tree
            v-if="sourceInputsParameter"
            v-model="showListView"
            ref="treeSelector"
            :modelName="sourceInputsParameter.treeModelName"
            :title="sourceInputsParameter.label"
            :enableMulti="treeMulti"
            :defaultValue="+sourceInputsParameter.default_value"
            :treeRootNodeValue="treeRootNodeValue"
            :setDefaultValue2RootCode="$attrs.setDefaultValue2RootCode"
            :label="label"
            :treePrefilters="treePrefilters"
            :selectedKeys="selectedKeys"
            :prefilter="prefilterData"
            :defaultExpandKeys="defaultExpandKeys"
            :treeConfig="treeConfig"
            @changeValue="handleDialogConfirm"
        ></dialog-tree>
    </div>
</template>

<script lang="ts">
    import { last, map } from "lodash"
    import { TreeTypes } from "uniplat-sdk"
    import { Tree } from "uniplat-sdk/build/main/model/tree/tree"
    import {
        Component,
        InjectReactive,
        Prop,
        Ref,
        Watch,
    } from "vue-property-decorator"
    import { BaseFiled } from "../base-filed"
    import DialogTree from "./dialog-tree.vue"

    @Component({ components: { DialogTree } })
    export default class VIntentSearch extends BaseFiled {
        @Ref()
        private readonly treeSelector!: DialogTree

        @Prop()
        placeholder!: string

        @Prop({ default: () => ({}) })
        private prefilterData!: Record<string, any>

        @Prop({ default: () => null })
        private treeConfig!: Record<string, any>

        @Prop({ default: false })
        inFilter!: boolean

        private treeApi!: Tree

        @InjectReactive({ from: "formCompData", default: () => ({}) })
        formCompData!: Record<string, any>

        private get treeRootNodeValue() {
            return (
                // this.sourceInputsParameter?.default_value ||
                this.sourceInputsParameter?.treeRootNode
            )
        }

        private currentValue = ""

        private text = ""

        private selectedList: TreeTypes.Node[] = []
        private selectedKeys: string[] | number[] = []
        private defaultExpandKeys: string[] = []

        private selectText = ""

        private showListView = false

        private get label() {
            return `选择` + (this.sourceInputsParameter?.label || "父节点")
        }

        private get display() {
            return this.selectText || this.text
        }

        private get treePrefilters() {
            return this.sourceInputsParameter?.ext_properties?.treePrefilters || []
        }

        private get treeMulti() {
            return (
                !!this.sourceInputsParameter?.treeMulti || !!this.$attrs.treeMulti
            )
        }

        @Watch("value", { immediate: true })
        protected onValueChange(cur: any, old: any) {
            this.currentValue = cur
            if (old || this.inFilter) {
                this.selectedKeys = this.getSelectedKeys()
            }
            this.selectText = this.getSelectText()
        }

        mounted() {
            this.text = this.sourceInputsParameter?.ext_properties.text
            if (this.sourceInputsParameter?.treeModelName) {
                this.treeApi = new Tree(this.sourceInputsParameter?.treeModelName)
                if (this.sourceInputsParameter.default_value) {
                    this.treeApi
                        .getNodeAllAncestor(
                            this.sourceInputsParameter.default_value || ""
                        )
                        .then((r: any) => {
                            this.defaultExpandKeys = map(
                                r.idList,
                                (e) => +e.id
                            ) as any
                            setTimeout(() => {
                                const fn = this.treeConfig?.handlerSelectedKeys
                                this.selectedKeys = fn
                                    ? fn()
                                    : [...this.defaultExpandKeys] || [
                                          ...this.defaultExpandKeys,
                                      ]
                                if (this.treeMulti) {
                                    const fn = this.treeConfig?.handlerSelectedKeys
                                    this.selectedList = fn ? fn() : []
                                    this.selectText = this.getSelectText()
                                } else {
                                    this.selectText = (
                                        last(r.list) as any
                                    )?.display_name
                                }
                            }, 100)
                        })
                }
            }
        }

        private handleDialogConfirm(dataList: TreeTypes.Node[]) {
            this.selectedList = dataList
            if (this.treeMulti) {
                if (this.$attrs.resultType === "string") {
                    this.onInput(
                        this.selectedList.map((i: TreeTypes.Node) => i.id).join(",")
                    )
                } else {
                    this.onInput(this.selectedList.map((i: TreeTypes.Node) => i.id))
                }
            } else {
                this.onInput(this.selectedList[0].id)

                if (this.selectedList[0].id === this.value) {
                    // this.selectedKeys = this.getSelectedKeys()
                    this.selectText = this.getSelectText()
                }
            }
        }

        private getSelectedKeys() {
            const defaultValue = ((this.currentValue + "") as string) || ""
            return defaultValue ? defaultValue?.split(",") : []
        }

        private getSelectText() {
            if (!this.selectedKeys.length) {
                return ""
            }
            if (this.selectedList.length == 1 && !this.treeMulti) {
                return this.selectedList[0].display
            }
            return `共${this.selectedList.length}条`
        }

        clickBtn() {
            if (this.$attrs.disabled) return
            this.text = ""
            this.onInput("")
            this.clearText()
        }

        clearText() {
            this.selectedList = []
            this.treeSelector.reset()
            const ext_properties = this.sourceInputsParameter?.ext_properties
            this.$emit("changeSourceInputsParameter", {
                ...this.sourceInputsParameter,
                ext_properties: ext_properties
                    ? { ...ext_properties, text: "" }
                    : ext_properties,
            })
        }

        beforeDestroy() {
            this.clearText()
        }

        clickInput() {
            if (this.$attrs.disabled) return
            this.$nextTick(() => {
                this.showListView = true
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .cursor-pointer {
        ::v-deep .el-input__inner {
            cursor: pointer !important;
        }
    }
    .clear-btn {
        min-width: 50px !important;
    }
</style>
