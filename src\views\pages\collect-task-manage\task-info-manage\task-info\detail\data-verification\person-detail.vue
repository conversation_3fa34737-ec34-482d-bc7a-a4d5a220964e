<template>
    <div class="core-ui-table-container" :key="refreshQueryParams">
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
        </div>

        <!-- 外呼详情页面 -->
        <div class="list-item d-flex flex-column">
            <!-- 个人信息 -->
            <div class="section-item">
                <div class="section-title d-flex align-items-center">
                    个人信息
                    <el-button type="text"> 查看劳动力详情 </el-button>
                </div>
                <div class="section-content d-flex flex-wrap">
                    <div class="item d-flex">
                        <div class="item-label">姓名：</div>
                        <div class="item-value">
                            <span>{{ fieldGroupsMap.姓名 || "-" }}</span>
                        </div>
                    </div>
                    <div class="item d-flex">
                        <div class="item-label">联系电话：</div>
                        <div class="item-value">
                            <span>{{ fieldGroupsMap.手机号码 || "-" }}</span>
                        </div>
                    </div>
                    <div class="item d-flex">
                        <div class="item-label">身份证号：</div>
                        <div class="item-value">
                            <span>{{ fieldGroupsMap.身份证号码 || "-" }}</span>
                        </div>
                    </div>
                    <div class="item d-flex">
                        <div class="item-label">性别：</div>
                        <div class="item-value">
                            <span>{{
                                getGenderText(fieldGroupsMap.性别)
                            }}</span>
                        </div>
                    </div>
                    <div class="item d-flex">
                        <div class="item-label">年龄：</div>
                        <div class="item-value">
                            <span
                                >{{
                                    calculateAge(fieldGroupsMap.身份证号码)
                                }}岁</span
                            >
                        </div>
                    </div>
                    <div class="item d-flex">
                        <div class="item-label">管理区域：</div>
                        <div class="item-value">
                            <span>{{ getManagementArea() }}</span>
                        </div>
                    </div>
                    <div class="item d-flex">
                        <div class="item-label">户籍地：</div>
                        <div class="item-value">
                            <span>{{ getHouseholdLocation() }}</span>
                        </div>
                    </div>
                    <div class="item d-flex">
                        <div class="item-label">常住地：</div>
                        <div class="item-value">
                            <span>{{ getCurrentResidence() }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 核验信息 -->
            <div class="section-item">
                <div class="section-title d-flex align-items-center">
                    核验信息
                    <div class="d-flex align-items-center">
                        <el-button type="text"> 查看问卷 </el-button>
                        <el-button type="text"> 外呼录音 </el-button>
                    </div>
                </div>
                <div class="section-content d-flex flex-wrap">
                    <div class="item d-flex">
                        <div class="item-label">文化程度：</div>
                        <div class="item-value">
                            <span>{{ fieldGroupsMap.文化程度 || "-" }}</span>
                        </div>
                    </div>
                    <div class="item d-flex">
                        <div class="item-label">就业方式：</div>
                        <div class="item-value">
                            <span>{{ fieldGroupsMap.就业方式 || "-" }}</span>
                        </div>
                    </div>
                    <div class="item d-flex">
                        <div class="item-label">就业地：</div>
                        <div class="item-value">
                            <span>{{ getEmploymentLocation() }}</span>
                        </div>
                    </div>
                    <div class="item d-flex">
                        <div class="item-label">外呼时间：</div>
                        <div class="item-value">
                            <span>{{ getOutboundTime() }}</span>
                        </div>
                    </div>
                    <div class="item d-flex">
                        <div class="item-label">外呼次数：</div>
                        <div class="item-value">
                            <span>{{ getOutboundCount() }}</span>
                        </div>
                    </div>
                    <div class="item d-flex">
                        <div class="item-label">核验标签：</div>
                        <div class="item-value">
                            <span>{{ getVerificationTags() }}</span>
                        </div>
                    </div>
                    <div class="item d-flex">
                        <div class="item-label">质检员：</div>
                        <div class="item-value">
                            <span>{{ fieldGroupsMap.更新人 || "-" }}</span>
                        </div>
                    </div>
                    <div class="item d-flex">
                        <div class="item-label">质检时间：</div>
                        <div class="item-value">
                            <span>{{ fieldGroupsMap.更新时间 || "-" }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import {
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "@/views/pages/single-page/components/tags-view"
    import { Component, Vue } from "vue-property-decorator"

    @Component({
        name: routesMap.collectTaskManage.taskInfoManage.dataVerification
            .personDetail,
        components: { TableContainer, CommonTable },
    })
    export default class PersonDetail extends Vue {
        private breadcrumbs: BreadcrumbItem[] = []

        refreshConfig = {
            fun: this.refresh,
            name: routesMap.collectTaskManage.taskInfoManage.dataVerification
                .personDetail,
        }

        private refresh() {
            console.log("refresh")
        }

        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                ...getCacheBreadcrumbsByRoutePath(this.$route.query.from as string),
                {
                    label: "人员详情",
                },
            ]
            updateTagItem({
                name: routesMap.collectTaskManage.taskInfoManage.dataVerification
                    .personDetail,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        private fieldGroupsMap: Record<string, string> = {}

        private queryDetail() {
            const modelName = this.$route.query.model as string
            const detailName = this.$route.query.detail as string
            sdk.core
                .model(modelName || "data_verify_source_data")
                .detail2(this.$route.query.id as string, detailName || "")
                .query()
                .then((res) => {
                    const {
                        meta: { header },
                        row,
                    } = res

                    const fieldGroupsMap = header.field_groups.reduce(
                        (acc: any, group: any) => {
                            acc[group.label] = group.template
                            return acc
                        },
                        {}
                    )
                    this.fieldGroupsMap = fieldGroupsMap
                })
                .catch((error) => {
                    console.error("查询详情失败:", error)
                })
        }

        // 获取性别文本
        private getGenderText(gender: string): string {
            if (!gender) return "未知"
            return gender === "1" ? "男" : gender === "2" ? "女" : gender
        }

        // 根据身份证号计算年龄
        private calculateAge(idNumber: string): number {
            if (!idNumber || idNumber.length !== 18) return 0
            const birthYear = parseInt(idNumber.substring(6, 10))
            const currentYear = new Date().getFullYear()
            return currentYear - birthYear
        }

        // 获取管理区域
        private getManagementArea(): string {
            const province = this.fieldGroupsMap.省 || ""
            const city = this.fieldGroupsMap.市 || ""
            const county = this.fieldGroupsMap.县 || ""
            const town = this.fieldGroupsMap.镇 || ""
            const village = this.fieldGroupsMap.村 || ""

            return [province, city, county, town, village].filter(Boolean).join("")
        }

        // 获取户籍地
        private getHouseholdLocation(): string {
            return this.getManagementArea()
        }

        // 获取常住地
        private getCurrentResidence(): string {
            return this.getManagementArea()
        }

        // 获取就业地点
        private getEmploymentLocation(): string {
            const province = this.fieldGroupsMap["就业地点(省)"] || ""
            const city = this.fieldGroupsMap["就业地点(市)"] || ""
            const county = this.fieldGroupsMap["就业地点(县)"] || ""

            return [province, city, county].filter(Boolean).join("")
        }

        // 获取外呼时间
        private getOutboundTime(): string {
            return this.fieldGroupsMap.更新时间 || "2024-01-01 08:00"
        }

        // 获取外呼次数
        private getOutboundCount(): string {
            return "2"
        }

        // 获取核验标签
        private getVerificationTags(): string {
            const tags = []
            if (
                this.fieldGroupsMap["就业地点(省)"] &&
                this.fieldGroupsMap["就业地点(省)"] !== this.fieldGroupsMap.省
            ) {
                tags.push("就业地不正确")
            }
            if (!this.fieldGroupsMap.就业方式) {
                tags.push("就业方式不正确")
            }
            return tags.length > 0 ? tags.join("、") : "无"
        }

        mounted() {
            this.setBreadcrumbs()
            this.queryDetail()
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";

    .list-item {
        background-color: #fff;
        padding: 20px;
    }

    .section-item {
        .section-title {
            width: 100%;
            height: 36px;
            background-color: #f8f8f8;
            padding: 0 20px;
            color: #000000;
            font-size: 16px;
            line-height: 16px;
            font-weight: 600;
            justify-content: space-between;
        }

        .section-content {
            padding: 20px;
            .item {
                width: 33%;
                line-height: 2;
                display: flex;
                align-items: flex-start;

                .item-label {
                    color: #555555;
                    font-size: 14px;
                    padding-right: 10px;
                    min-width: 100px;
                }

                .item-value {
                    color: #222222;
                    font-size: 14px;
                    padding-right: 10px;
                }
            }
        }
    }
</style>
