import { BuildFormConfig, FormType } from "@/core-ui/component/form"
import { TableColumn } from "@/core-ui/component/table"
import { sdk } from "@/service"
import {
    CURRENT_REGION_NODE_LEVEL,
    CURRENT_TREE_REGION_NODE,
    levelCodes,
} from "./base"
import { prefilter } from "uniplat-sdk"

export function tableConfig(
    detailId: string,
    cb: Function,
    params?: string
): any {
    const regionCode = sessionStorage.getItem(CURRENT_TREE_REGION_NODE)
    const level = sessionStorage.getItem(CURRENT_REGION_NODE_LEVEL) || "1"
    const nKey = levelCodes[+level - 1]
    const query = {
        request: (domainService: any, payload: any) => {
            return new Promise((resolve) => {
                sdk.core
                    .domainService(
                        "data_products",
                        "grid_collect_task_api",
                        params || "collect_task_detail_region_statistics2"
                    )
                    .request(domainService, {
                        data: {
                            ...payload.data,
                            regionLevelName: nKey,
                            region_code: regionCode,
                            root_task_id: detailId,
                        },
                    })
                    .then((r: any) => {
                        cb(r)
                        resolve({
                            ...r,
                            total_count: r.total,
                        })
                    })
            })
        },
    }

    return {
        domainService: query,
        defaultPageSize: 1000,
    }
}

export const columns = (
    showTarget: Boolean,
    showFillTarget: Boolean
): any[] => {
    return [
        {
            label: "下级区域",
            prop: "region_name",
            minWidth: "250px",
            showOverflowTip: true,
            fixed: "left",
        },
        {
            label: "新增目标数量",
            prop: "target_add_num",
            minWidth: "180px",
            showOverflowTip: true,
        },
        {
            label: "任务总况",
            prop: "任务总况",
            showOverflowTip: true,
            children: [
                {
                    label: "需采集人数",
                    prop: "task_num",
                    minWidth: "120px",
                    showOverflowTip: true,
                },
                {
                    label: "待采集人数",
                    minWidth: "120px",
                    prop: "task_wait_num",
                    showOverflowTip: true,
                },
                {
                    label: "已采集人数",
                    minWidth: "120px",
                    prop: "task_collected_num",
                    showOverflowTip: true,
                },
            ],
        },

        {
            label: "分配情况",
            prop: "分配情况",
            showOverflowTip: true,
            children: [
                {
                    label: "补采数据",
                    prop: "task_fill_num",
                    minWidth: "120px",
                    showOverflowTip: true,
                },
                {
                    label: "更新数据",
                    minWidth: "120px",
                    prop: "task_update_num",
                    showOverflowTip: true,
                },
            ],
        },

        {
            label: "审核情况",
            prop: "审核情况",
            showOverflowTip: true,
            children: [
                {
                    label: "待审核数量",
                    prop: "wait_audit_num",
                    minWidth: "120px",
                    showOverflowTip: true,
                },
                {
                    label: "审核通过(次数)",
                    minWidth: "136px",
                    prop: "audit_success_num",
                    showOverflowTip: true,
                },
                {
                    label: "审核通过(人数)",
                    minWidth: "136px",
                    prop: "collect_num",
                    showOverflowTip: true,
                },
            ],
        },

        {
            label: "疑点数据",
            prop: "疑点数据",
            showOverflowTip: true,
            children: [
                {
                    label: "联系不上",
                    prop: "not_contact_num",
                    minWidth: "120px",
                    showOverflowTip: true,
                },
                {
                    label: "非本区域居民",
                    minWidth: "136px",
                    prop: "not_local_num",
                    showOverflowTip: true,
                },
                {
                    label: "其他区域采集",
                    minWidth: "136px",
                    prop: "other_collect_num",
                    showOverflowTip: true,
                },
            ],
        },

        {
            label: "采集情况",
            prop: "采集情况",
            showOverflowTip: true,
            children: [
                {
                    label: "总完成进度",
                    prop: "collect_process",
                    minWidth: "120px",
                    showOverflowTip: true,
                },
                {
                    label: "更新人数(更新进度)",
                    minWidth: "95px",
                    prop: "collect_update_num##collect_update_percentage",
                    showOverflowTip: true,

                    render: (h: any, row: any) => {
                        return h("div", [
                            h("div", row.collect_update_num),
                            h("div", row.collect_update_percentage),
                        ])
                    },
                },
                {
                    label: "补采人数(补采进度)",
                    minWidth: "95px",
                    prop: "collect_fill_num##collect_fill_percentage",
                    showOverflowTip: true,
                    render: (h: any, row: any) => {
                        return h("div", [
                            h("div", row.collect_fill_num),
                            h("div", row.collect_fill_percentage),
                        ])
                    },
                },
                {
                    label: "新增人数(新增进度)",
                    minWidth: "95px",
                    prop: "collect_add_num##collect_fill_percentage",
                    showOverflowTip: true,
                    render: (h: any, row: any) => {
                        return h("div", [
                            h("div", row.collect_add_num),
                            h("div", row.collect_add_percentage),
                        ])
                    },
                },
            ],
        },
        {
            label: "采集员",
            prop: "grid_user_num",
            showOverflowTip: true,
        },
    ].filter((i: any) => {
        if (i.prop === "target_add_num") {
            return showTarget
        }

        if (!showFillTarget) {
            if (i.prop === "分配情况") {
                i.children = i.children?.filter(
                    (i: any) => i.prop !== "task_fill_num"
                )
            }
            if (i.prop === "采集情况") {
                const needHide = ["collect_fill_num"]
                i.children = i.children?.filter(
                    (i: any) => !needHide.includes(i.prop)
                )
            }
        }
        return true
    })
}

export const columns2 = (): any[] => {
    return [
        {
            label: "组织机构",
            prop: "region_name",
            minWidth: "250px",
            showOverflowTip: true,
            fixed: "left",
        },
        {
            label: "任务情况",
            prop: "任务情况",
            showOverflowTip: true,
            children: [
                {
                    label: "已分配人数",
                    prop: "task_num",
                    minWidth: "120px",
                    showOverflowTip: true,
                },
                {
                    label: "待采集人数",
                    minWidth: "120px",
                    prop: "task_wait_num",
                    showOverflowTip: true,
                },
                {
                    label: "待审核数量",
                    minWidth: "120px",
                    prop: "wait_audit_num",
                    showOverflowTip: true,
                },
                {
                    label: "审核通过数量",
                    minWidth: "140px",
                    prop: "audit_success_num",
                    showOverflowTip: true,
                },
                {
                    label: "任务内更新人数",
                    minWidth: "140px",
                    prop: "collect_num_in_task",
                    showOverflowTip: true,
                },
                {
                    label: "疑点数据总量",
                    minWidth: "140px",
                    prop: "doubtful_num",
                    showOverflowTip: true,
                },
                {
                    label: "任务进度",
                    minWidth: "120px",
                    prop: "task_process",
                    showOverflowTip: true,
                },
            ],
        },

        {
            label: "总采集人数",
            prop: "总采集人数",
            showOverflowTip: true,
            children: [
                {
                    label: "已采集人数",
                    prop: "task_collected_num",
                    minWidth: "120px",
                    showOverflowTip: true,
                },
                {
                    label: "任务内更新人数",
                    minWidth: "140px",
                    prop: "collect_num_in_task",
                    showOverflowTip: true,
                },
                {
                    label: "任务外更新人数",
                    minWidth: "140px",
                    prop: "collect_num_out_of_task",
                    showOverflowTip: true,
                },
                {
                    label: "新增人数",
                    minWidth: "120px",
                    prop: "collect_add_num",
                    showOverflowTip: true,
                },
            ],
        },

        {
            label: "疑点数据",
            prop: "疑点数据",
            showOverflowTip: true,
            children: [
                {
                    label: "疑点数据总量",
                    prop: "doubtful_num",
                    minWidth: "140px",
                    showOverflowTip: true,
                },
                {
                    label: "联系不上人数",
                    minWidth: "136px",
                    prop: "not_contact_num",
                    showOverflowTip: true,
                },
                {
                    label: "非本区域人数",
                    minWidth: "136px",
                    prop: "not_local_num",
                    showOverflowTip: true,
                },
                {
                    label: "被别人采集的人数",
                    minWidth: "156px",
                    prop: "collect_by_others_num",
                    showOverflowTip: true,
                },
            ],
        },
        {
            label: "采集进度",
            prop: "collect_process",
            showOverflowTip: true,
        },
        {
            label: "采集员",
            prop: "grid_user_num",
            showOverflowTip: true,
        },
    ]
}

export function createFormConfig(
    id: string,
    preFilters: prefilter[]
): BuildFormConfig {
    const forms: BuildFormConfig["forms"] = [
        {
            label: "目标采集数",
            type: FormType.InputNumber,
            prop: "target_num",
            option: {
                useNumber: true,
            },
            required: true,
        },
    ]
    return {
        sdkModel: "collect_task",
        sdkAction: "update_target_num",
        initialParams: {
            prefilters: preFilters,
        },
        id: +id,
        forms,
    }
}
