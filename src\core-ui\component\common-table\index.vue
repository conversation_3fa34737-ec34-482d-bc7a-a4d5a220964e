<template>
    <div class="common-table">
        <el-table
            ref="commonTable"
            v-bind="tableConfig"
            :data="data"
            :empty-text="emptyText"
            tooltip-effect="light"
            :stripe="tableConfig.stripe || tableConfig.stripe === undefined"
            @selection-change="handleSelectionChange"
            @select="select"
            @cell-click="cellClick"
            @select-all="$emit('selectAll', $event)"
        >
            <template v-for="item in columns">
                <table-column-container
                    :key="item.prop"
                    :item="item"
                    :slots="slots"
                    :getVNode="getVNode"
                    v-if="!item.hide"
                    @selectionTypeChange="selectionTypeChange"
                >
                    <template v-for="slot in slots" #[slot]="scope">
                        <slot :name="slot" v-bind="scope" />
                    </template>
                </table-column-container>
            </template>
        </el-table>
    </div>
</template>

<script lang='ts'>
    import { Table as ElTable } from "element-ui"
    import { isArray, keys, includes } from "lodash"
    import { VNodeChildren } from "vue"
    import { Component, Prop, Ref, Vue, Watch } from "vue-property-decorator"
    import { getCellvalue, TableColumn } from "../table"
    import TableColumnContainer from "./table-column.vue"

    @Component({ components: { TableColumnContainer } })
    export default class CommonTable extends Vue {
        @Prop({ default: () => [] })
        private data!: any[]

        @Prop({ default: () => [] })
        private columns!: TableColumn[]

        @Prop({ default: () => ({ stripe: true }) })
        private tableConfig!: ElTable

        @Ref()
        public commonTable!: ElTable

        @Prop({
            default: "id",
        })
        public selectKey!: string

        @Prop({
            default: "暂无数据",
        })
        public emptyText!: string

        private get slots() {
            return keys(this.$scopedSlots)
        }

        private handleSelectionChange(rows: any[]) {
            this.$emit("handleSelectionChange", {
                rows,
                ids: rows.map((e) => e.id),
            })
        }

        private select(selection: any[], row: any) {
            this.$emit(
                "select",
                selection
                    .map((e) => e[this.selectKey])
                    .includes(row[this.selectKey]),
                row
            )
        }

        public getVNode(column: TableColumn, row: Record<string, string>) {
            const value = getCellvalue(row, column, column.emptyValue)
            const h = this.$createElement
            let children: VNodeChildren = (
                value ??
                (column.emptyText || "-")
            )?.toString()
            if (column.render) {
                const v = column.render(h, row, column)
                if (isArray(v)) {
                    children = v
                } else {
                    children = [v]
                }
            }
            return h(
                column.renderTag || "span",
                {
                    class: `table-column ${
                        !value && !column.render ? "text-99" : ""
                    }`,
                },
                children
            )
        }

        private cellClick() {
            this.$emit("cellClick", ...arguments)
        }

        selectionTypeChange(e: any) {
            try {
                const parent: any = this.$parent
                const fn =
                    parent?.selectionTypeChange ||
                    parent.$parent?.selectionTypeChange
                fn && fn(e)
            } catch (e) {
                this.$emit("selectionTypeChange", e)
            }
        }
    }
</script>

<style lang='less'>
    @import "~@/css/variables.less";
    .handler-btn {
        color: @main-color;
        cursor: pointer;
        user-select: none;
        &:not(:first-child) {
            margin-left: 10px;
        }
    }
</style>
