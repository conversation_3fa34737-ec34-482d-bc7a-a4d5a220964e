import { TableColumn, TableConfig } from "@/core-ui/component/table"
import { sdk } from "@/service"
import { DetailController } from "../../base"

export function tableConfig(pageListName: string): TableConfig {
    console.log("pageListName:", pageListName)

    const c = DetailController.getPagePreFilterMetaInDetail(pageListName)
    console.log("c:", c)

    return {
        model: sdk.core.model(c.modelName).list(c.listName),
        filter: [],
        useRemoteFilter: true,
        preFilter: c.preFilter2Obj,
        defaultPageSize: 6,
    }
}
export const columns = (): TableColumn[] => [
    {
        label: "联系人",
        prop: "name",
        width: "180px",
        showOverflowTip: true,
    },
    {
        label: "联系电话",
        prop: "phone_number",
    },
    {
        label: "外呼时间",
        prop: "update_time_label",
    },
    {
        label: "外呼状态",
        prop: "status_label",
    },
    {
        label: "外呼录音",
        prop: "外呼录音",
        width: "280px",
        render: (h, row) => {
            return h("div", {
                domProps: {
                    innerHTML: row.field_groups[4],
                },
            })
        },
    },
    {
        label: "外呼问卷",
        prop: "外呼问卷",
    },
    {
        label: "操作",
        prop: "h",
        width: "120px",
        fixed: "right",
    },
]
