<template>
    <div class="detail-container">
        <div class="u-flex u-row-between u-p-x-20">
            <div></div>
            <el-button
                v-for="action in actions"
                :key="action.name"
                type="primary"
                @click="handleAction(action)"
            >
                {{ action.label }}
            </el-button>
        </div>

        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            @getRows="getRows"
            class="container-index container shadow"
        >
            <div slot="table" slot-scope="{ data }" class="u-p-20 bg-white">
                <common-table :data="data" :columns="columns">
                    <div slot="h" slot-scope="scope">
                        <el-button
                            type="text"
                            @click="goDetail(scope.row._access_key)"
                        >
                            详情
                        </el-button>
                    </div>
                </common-table>
            </div>
        </table-container>
        <action-container
            v-model="showActionContainer"
            ref="actionContainer"
            :action="action"
            :modelName="modelName"
            :selected_list="selected_list"
            :prefilters="prefilters"
            :filters="filters"
            @success="handleActionSuccess"
        />
    </div>
</template>

<script lang="ts">
import CommonTable from "@/core-ui/component/common-table/index.vue"
import { TableColumn, TableConfig } from "@/core-ui/component/table"
import { BaseTableController } from "@/core-ui/component/table/base-table"
import TableContainer from "@/core-ui/component/table/container.vue"
import { routesMap } from "@/router/direction"
import { sdk } from "@/service"
import { pageLoading } from "@/views/controller"
import {
    buildConfig4RemoteMeta,
    getShowBtn4List,
    getShowBtn4Page,
} from "@/views/pages/collect-task-manage/components/build-table"
import { cloneDeep } from "lodash"
import { action, Meta } from "uniplat-sdk"
import { Component, Prop, Ref } from "vue-property-decorator"
import {
    CURRENT_TREE_REGION_NODE,
    DetailController,
    getBtnViewsByMetaActions,
} from "../../base"
import { DetailRow } from "../../model"
import ActionContainer from "./action-container.vue"
@Component({
    components: {
        TableContainer,
        CommonTable,
        ActionContainer,
    },
})
export default class DataVerification extends BaseTableController<any> {
    @Prop({ default: () => {} })
    private detailRow!: DetailRow

    tableConfig: TableConfig | null = null

    private columns: TableColumn[] = []

    private detailId = ""

    private viewActions: action[] = []

    private checkIds: string[] = []

    private showBtn = false

    private rows: any[] = []

    private meta: Meta["meta"] | null = null

    private get actions() {
        return this.meta?.actions || []
    }

    public noticeRegionIdUpdate(init: boolean) {
        this.detailId = this.$route.query.id as string
        if (init) {
            return this.init()
        }
        this.tableConfig = null
        this.$nextTick(() => {
            this.init()
        })
    }

    private getRows(
        rows: { intents: { name: string }[]; id: { value: string } }[]
    ) {
        const cacheRows = cloneDeep(this.rows)
        const resArr = cacheRows.filter((i) => {
            return !(rows || []).find((j) => j.id.value === i.id.value)
        })
        resArr.push(...(rows || []))
        this.rows = resArr
    }

    private init() {
        DetailController.getDetail(this.detailId).then(() => {
            const c =
                DetailController.getPagePreFilterMetaInDetail(
                    "data_verify_task"
                )
            buildConfig4RemoteMeta(c.modelName, c.listName, {
                disabledOpt: false,
                disabledFilter: true,
                useLabelWidth: true,
                optColumn: {
                    label: "操作",
                    prop: "h",
                    fixed: "right",
                    minWidth: "150px",
                },
                addSelector: true,
                prefilters: c?.preFilter2Obj || {},
                // customLabelWidths: {
                //     身份证号: 220,
                //     管理区域: 280,
                //     采集员备注区划: 280,
                // },
                useRowFieldGroups: true,
            }).then((r) => {
                this.buildConfig(r)
            })
        })
    }

    private buildConfig(r: any) {
        this.meta = r.meta
        const tableConfig: any = r.tableConfig

        tableConfig.filter = r.filter
        tableConfig.predict = {
            ...r.tableConfig.predict,
            actions: "actions",
        }
        this.tableConfig = null

        this.$nextTick(() => {
            this.tableConfig = tableConfig
        })

        this.columns = r.columns
    }

    private getViewActionDisplayInfo(key: string) {
        return !!getBtnViewsByMetaActions(this.viewActions, key)
    }

    private goDetail(id: number) {
        this.$router.push({
            name: routesMap.collectTaskManage.taskInfoManage.dataVerification
                .detail,
            query: {
                id: id + "",
                from: this.$route.name,
                task_detail_id: this.detailId,
            },
        })
    }

    private action: action | null = null
    private modelName = "data_verify_task"
    private selected_list: { id: string; v: number }[] = []
    private prefilters: { property: string; value: string | number }[] = []
    private filters: Record<string, any[]> = {}

    private handleAction(action: action) {
        console.log("action", action)
        console.log(this.detailRow)

        this.action = action
        // this.selected_list = [{ id: this.detailRow.id, v: this.detailRow.v }]
        this.prefilters = [
            {
                property: "collect_root_task_id",
                value: this.detailRow.root_task_id,
            },
            {
                property: "is_del",
                value: 0,
            },
        ]
        if (action.action_name === "insert") {
            this.filters = {
                date_filters: [
                    { property: "verify_time" },
                    { property: "confirm_time" },
                ],
            }
        }
        this.$nextTick(() => {
            this.showActionContainer = true
        })
    }

    private showActionContainer = false
    private handleActionSuccess() {
        this.showActionContainer = false
        this.refreshList()
    }
}
</script>

<style lang="less" scoped>
@import "~@/css/variables.less";
@import "~@/css/table-container.less";

.detail-container {
    gap: 20px;
    min-height: 610px;
    padding-top: 20px;

    /deep/ .el-button {
        min-width: auto !important;
    }
}

.ml-auto {
    margin-left: auto;
}

ul,
li {
    margin: 0;
    padding: 0;
    list-style: none;
    line-height: 1.5;
}
</style>
