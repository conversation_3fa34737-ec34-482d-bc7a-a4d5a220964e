<template>
    <div class="detail-top-box">
        <div class="detail-top-header">
            <div class="detail-top-title">{{ this.detailRow.title }}</div>
            <div class="edit-icon">
                <!-- <i
                    class="el-icon-edit"
                    @click="updateInfo"
                    v-if="getViewActionDisplayInfo('update')"
                ></i> -->
            </div>
        </div>
        <el-collapse-transition>
            <div v-show="isExpanded">
                <detail-row-col
                    :labelStyle="labelStyle"
                    :list="items"
                    class="u-p-x-20 u-p-t-20"
                >
                </detail-row-col>
            </div>
        </el-collapse-transition>
        <div class="expand-container">
            <span class="expand-btn" @click="toggleExpand">
                {{ isExpanded ? "收起" : "展开" }}
                <i
                    :class="[
                        'el-icon-arrow-down',
                        { 'expand-btn-arrow-up': isExpanded },
                    ]"
                ></i>
            </span>
        </div>
        <ViewPop
            v-model="displayViewPop"
            :row="item"
            modelName="collect_task"
        ></ViewPop>
    </div>
</template>

<script lang="ts">
import { ColItem } from "@/views/components/detail-row-col"
import { Component, Prop, Vue } from "vue-property-decorator"
import { DetailRow } from "../model"
import DetailRowCol from "@/views/components/detail-row-col/index.vue"
import { routesMap } from "@/router/direction"
import ViewPop from "@/views/pages/collect-task-manage/business-manage/components/view-pop.vue"
import { DetailController } from "../base"
import { config, EnvProject } from "@/config"
import {
    DCollectSysConfig,
    getSys4DCollectByKey,
} from "../../../collect-task-config"

@Component({
    components: {
        DetailRowCol,
        ViewPop,
    },
})
export default class TemplateView extends Vue {
    @Prop({ default: () => {} })
    private detailRow!: DetailRow

    private displayViewPop = false

    private isExpanded = false

    private toggleExpand() {
        this.isExpanded = !this.isExpanded
    }

    // private openView() {
    //     localStorage.setItem(
    //         "cacheCollectionTaskRootSchemaInfo",
    //         this.detailRow.business_schema_info
    //     )
    //     this.displayViewPop = true
    // }

    // private getViewActionDisplayInfo(key: string) {
    //     return DetailController.getBtnViewsInDetail(key)
    // }

    // private getBtnViewsInDetailIntents(key: string) {
    //     return DetailController.getBtnViewsInDetailIntents(key)
    // }

    private get item() {
        return {
            id: this.detailRow.id,
            title: "采集信息项",
        }
    }

    private items: ColItem[] = []
    private get labelStyle() {
        return {
            minWidth: "78px",
            textAlign: "left",
            color: "#555",
        }
    }

    created() {
        this.setItems()
    }

    // private updateInfo() {
    //     this.$router.push({
    //         name: routesMap.collectTaskManage.taskInfoManage.taskInfo.create,
    //         query: {
    //             from: this.$route.name,
    //             id: this.detailRow.id,
    //             edit: "1",
    //         },
    //     })
    // }

    private async setItems() {
        const detailRow: any = this.detailRow || {}

        const showTarget = await getSys4DCollectByKey(
            DCollectSysConfig.显示任务数量与目标数量
        ).catch(() => {})

        this.items = [
            {
                label: "采集模版：",
                value: detailRow.collect_root_task_business_schema_title,
                span: 6,
            },
            {
                label: "所属区域：",
                value: this.getRegionData(),
                span: 6,
            },
            // {
            //     label: "负责人：",
            //     value: detailRow.handler_user_info,
            //     span: 6,
            // },
            {
                label: "起止时间：",
                value:
                    this.handleDate(detailRow.start_date) +
                    " 至 " +
                    this.handleDate(detailRow.end_date),
                span: 6,
            },
            {
                label: "状态：",
                value: detailRow.status_label,
                span: 6,
            },
            // { label: "执行周期：", value: detailRow.cycle_label, span: 8 },
            {
                label: "任务目标：",
                value: (detailRow.target_num || 0) * 1 || "-",
                span: 6,
            },
            {
                label: "描述：",
                value: detailRow.description,
                span: 12,
            },
        ].filter((i) => {
            if (i.label === "任务目标：") {
                return showTarget
            }
            return true
        })
    }

    private getRegionData() {
        const detailRow: any = this.detailRow || {}
        return (
            (detailRow.province_name || "") +
            (detailRow.city_name || "") +
            (detailRow.district_name || "") +
            (detailRow.town_name || "") +
            (detailRow.village_name || "")
        )
    }

    private handleDate(date: string) {
        if (!date) {
            return "-"
        }
        return date.slice(0, 10)
    }
}
</script>

<style lang="less" scoped>
@import "~@/css/variables.less";
@import "~@/css/table-container.less";

.detail-top-header {
    padding-left: 20px;
    padding-right: 20px;
    display: flex;
    align-items: center;

    width: 100%;
}

.detail-top-title {
    font-weight: 600;
    font-size: 18px;
    color: #222222;
    line-height: 18px;
}

.detail-top-box {
    background-color: #fff;
    margin-bottom: 20px;
    padding-top: 20px;
    width: 100%;
    padding-bottom: 20px;
    position: relative;
}

.detail-top {
    margin-left: auto;

    /deep/ .el-button {
        min-width: 0px !important;
    }
}

.edit-icon {
    font-size: 20px;
    margin-left: 10px;
    cursor: pointer;

    &:hover {
        color: rgba(87, 130, 236, 1);
    }
}

.expand-container {
    position: absolute;
    bottom: 0;
    right: 0;
    left: 0;
    margin: auto;
    text-align: center;
}

.expand-btn {
    cursor: pointer;
    color: rgba(116, 126, 178, 1);
    background-color: rgba(255, 255, 255, 1);
    height: 25px;
    width: 68px;
    line-height: 25px;
    border-radius: 3px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transform: translateY(50%);

    .expand-btn-arrow-up {
        transform: rotate(180deg);
    }

    i {
        transition: transform 0.3s;
        margin-left: 5px;
    }
}
</style>
