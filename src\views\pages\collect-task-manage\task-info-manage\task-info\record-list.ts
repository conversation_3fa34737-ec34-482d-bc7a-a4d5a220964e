import { TableColumn, TableConfig } from "@/core-ui/component/table"
import { sdk } from "@/service"
import { DetailController } from "./base"
import { predict } from "@/views/pages/collect-task-manage/task-info-manage/collect-info-manage/index"
import { formatTime } from "@/utils/tools"
import { config, EnvProject } from "@/config"

export interface Row {
    /** 所属采集任务 */
    title: string
    /** 审核人 */
    name: number
    /** 身份证件类型 */
    id_card_type_remark: string
    /** 证件号码 */
    id_card: string
    /** 性别 */
    gender_remark: string
    /** 出生日期 */
    birth_date: string
    /** 联系电话 */
    mobile: string
    /** 民族 */
    ethnic_remark: string
    /** 户籍所在地-省 */
    household_province_code_remark: string
    /** 户籍所在地-地市 */
    household_city_code_remark: string
    /** 户籍所在地-区县 */
    household_district_code_remark: string
    /** 户籍所在地-街道/乡镇 */
    household_town_code_remark: string
    /** 户籍所在地-社区/村 */
    household_village_code_remark: string
    /** 户籍所在地-详细地址 */
    household_address: string
    /** 户口性质 */
    household_property_remark: string
    /** 户口本-户主姓名 */
    householder: string
    /** 户口本-编码 */
    householder_no: string
    /** 户口本-户主身份证号 */
    householder_id_card: string
    /** 户口本-与户主关系 */
    householder_relation: string
    /** 籍贯所在地 */
    hometown_place: string
    /** 婚姻状况 */
    marital_status_remark: string
    /** 现居住地-省 */
    residence_province_code_remark: string
    /** 现居住地-地市 */
    residence_city_code_remark: string
    /** 现居住地-区县 */
    residence_district_code_remark: string
    /** 现居住地-街道/乡镇 */
    residence_town_code_remark: string
    /** 现居住地-社区/村 */
    residence_village_code_remark: string
    /** 现居住地-详细地址 */
    residence_address: string
    /** 政治面貌 */
    political_status_remark: string
    /** 父母姓名 */
    parent_name: string
    /** 父母手机号 */
    parent_mobile: string
    /** 健康状况 */
    health_status_remark: string
    /** 残疾人标识 */
    deformed_status_remark: string
    /** 残疾人等级 */
    deformed_level: string
    /** 是否享受残疾人补贴 */
    deformed_allowance_remark: string
    /** 学历 */
    education_degree_remark: string
    /** 入学日期 */
    education_enrollment_date: string
    /** 毕业日期 */
    education_graduation_date: string
    /** 所学专业编码 */
    education_major_code: string
    /** 所学专业名称 */
    education_major_name: string
    /** 学校编码 */
    education_school_code: string
    /** 学校名称 */
    education_school_name: string
    /** 招生入学方式 */
    education_entrance_type_remark: string
    /** 学业结束方式 */
    education_graduation_type_remark: string
    /** 就业状态 */
    employment_status_remark: string
    /** 就业形式 */
    employment_type_remark: string
    /** 就业日期 */
    employment_date: string
    /** 就业行业 */
    employment_industry_remark: string
    /** 就业工种 */
    employment_work_kind_remark: string
    /** 工作地-省 */
    work_province_code_remark: string
    /** 工作地-地市 */
    work_city_code_remark: string
    /** 工作地-区县 */
    work_district_code_remark: string
    /** 工作地-街道/乡镇 */
    work_town_code_remark: string
    /** 工作地-社区/村 */
    work_village_code_remark: string
    /** 工作地-详细地址 */
    work_address: string
    /** 在外地工作类别 */
    work_out_place_type_remark: string
    /** 在外地工作年数 */
    work_out_place_year: string
    /** 年均薪资 */
    work_annual_salary: string
    /** 月均薪资 */
    work_monthly_salary: string
    /** 用人单位名称 */
    work_company_name: string
    /** 用人单位统一社会信用代码 */
    work_company_code: string
    /** 失业登记地类型 */
    unemployment_region_remark: string
    /** 失业原因 */
    unemployment_reason_remark: string
    /** 创业标识 */
    founder_status_remark: string
    /** 创业类型 */
    founder_type_remark: string
    /** 创业注册日期 */
    founder_date: number
    /** 创业带动就业人数 */
    founder_staff_size: string
    /** 创业企业经营范围 */
    founder_biz_scope: string
    /** 有无创业担保贷款需求 */
    founder_loan_demand_remark: string
    /** 创业扶持人员类别 */
    founder_support_type_remark: string
    /** 首次创业标识 */
    founder_first_remark: string
    /** 求职意向状态 */
    intention_status_remark: string
    /** 求职意向工作形式 */
    intention_kind_remark: string
    /** 期望入职日期 */
    intention_work_date: number
    /** 期望工作地-省 */
    intention_province_code_remark: string
    /** 期望工作地-地市 */
    intention_city_code_remark: string
    /** 期望工作地-区县 */
    intention_district_code_remark: string
    /** 期望工作地-街道/乡镇 */
    intention_town_code_remark: string
    /** 期望工作地-社区/村 */
    intention_village_code_remark: string
    /** 期望工作地范围 */
    intention_place_brief_remark: string
    /** 期望工作工种类别 */
    intention_work_type_code_remark: string
    /** 期望工作工种名称 */
    intention_work_type_name: string
    /** 期望月薪资收入 */
    intention_month_salary_remark: string
    /** 期望企业所属行业类别 */
    intention_industry_code_remark: string
    /** 期望企业所属行业名称 */
    intention_industry_name: string
    /** 是否有返乡就业意愿 */
    intention_back_home_status_remark: string
    /** 是否有创业意愿 */
    intention_founder_status_remark: string
    /** 社保缴纳地区类型 */
    insurance_area_type_remark: string
    /** 社保缴纳类型 */
    insurance_payment_type_remark: string
    /** 是否参加医疗保险 */
    insurance_medical_remark: string
    /** 是否参加养老保险 */
    insurance_endowment_remark: string
    /** 是否参加失业保险 */
    insurance_unemployment_remark: string
    /** 是否参加工伤保险 */
    insurance_injury_remark: string
    /** 是否参加生育保险 */
    insurance_maternity_remark: string
    /** 是否领取养老待遇 */
    insurance_endowment_receive_remark: string
    /** 返乡人员标识 */
    back_home_status_remark: string
    /** 返乡日期 */
    back_home_date: number
    /** 返乡前所在地-省 */
    back_home_from_province_code_remark: string
    /** 返乡前所在地-地市 */
    back_home_from_city_code_remark: string
    /** 返乡前所在地-区县 */
    back_home_from_district_code_remark: string
    /** 返乡前所在地地址 */
    back_home_from_address: string
    /** 是否就业困难人群 */
    special_hard_employment_remark: string
    /** 就业困难人群类型 */
    special_hard_employment_type_remark: string
    /** 是否零就业家庭 */
    special_family_unemployment_remark: string
    /** 是否退补渔民 */
    label_freshman_retired_remark: string
    /** 是否复原退伍军人 */
    label_veteran_remark: string
    /** 是否刑满释放人员 */
    label_ex_convict_remark: string
    /** 是否戒毒康复人员 */
    label_drug_rehabilitated_remark: string
    /** 是否高校毕业生 */
    college_graduate_status_remark: string
    /** 高校毕业日期 */
    college_graduate_date: number
    /** 是否2年内高校毕业生 */
    college_graduate_less_2_year_remark: string
    /** 是否参加过职业培训 */
    career_train_status_remark: string
    /** 职业培训的日期 */
    career_train_date: number
    /** 职业培训的内容 */
    career_train_content: string
    /** 是否有参加职业培训的意愿 */
    career_train_intention_status_remark: string
    /** 愿意参加职业培训的内容 */
    career_train_intention_content: string
    /** 职业资格名称 */
    career_cert_name: string
    /** 职业资格等级 */
    career_cert_level: string
    /** 职业资格获取日期 */
    career_cert_date: string
    /** 专业职称名称 */
    professional_cert_name: string
    /** 专业职称等级 */
    professional_cert_level: string
    /** 专业职称获取日期 */
    professional_cert_date: string
    /** 创建时间 */
    create_time: string
    /** 更新时间 */
    update_time: string
    /** 采集者类型 */
    collector_type: CollectorType
    /** 采集者类型[文本] */
    collector_type_label: string
    /** 采集者ID */
    collector_id: string
    /** 采集时间 */
    collect_time: string
    /** 采集的原始记录编号 */
    collect_source_id: number
    /** 审核状态 */
    audit_status: AuditStatus
    /** 审核状态[文本] */
    audit_status_label: string
    /** 审核时间 */
    audit_time: string
    /** 管理区域 */
    region_name: number
    id: number
    v: number
}

const enum CollectorType {
    采集员 = 1,
    居民个人 = 2,
    接口上报 = 3,
    文件上传 = 4,
    后台添加 = 5,
}

const enum AuditStatus {
    待审核 = 0,
    审核通过 = 1,
    审核未通过 = 2,
}

export function tableConfig(pageListName: string) {
    const c = DetailController.getPagePreFilterMetaInDetail(pageListName)
    return {
        model: sdk.core.model(c.modelName).list2(c.listName),
        filter: [],
        useRemoteFilter: true,
        preFilter: c.preFilter2Obj,
        defaultPageSize: 6,
        predict: predict,
    }
}

const baseColumns: TableColumn[] = [
    {
        label: "基本信息",
        prop: "name",
        width: "150px",
        showOverflowTip: true,
        render: (h, row) => {
            return h("div", [h("div", row["姓名"]), h("div", row["联系电话"])])
        },
    },
    {
        label: "户籍地",
        prop: "户籍所在地",
        minWidth: "400px",
        showOverflowTip: true,
    },
    {
        label: "信息更新状态",
        prop: "信息更新状态",
        showOverflowTip: true,
        minWidth: "130px",
    },
    {
        label: "采集员",
        prop: "采集者",
        showOverflowTip: true,
    },
    {
        label: "采集时间",
        prop: "采集时间",
        showOverflowTip: true,
        minWidth: "100px",
        formatter: (row) => {
            return formatTime.day(row["采集时间"])
        },
    },
    {
        label: "审核状态",
        prop: "审核状态",
        showOverflowTip: true,
        minWidth: "120px",
        fixed: "right",
    },
    {
        label: "审核反馈",
        prop: "审核反馈",
        showOverflowTip: true,
        minWidth: "80px",
        fixed: "right",
    },
    {
        label: "操作",
        prop: "h",
        minWidth: "120px",
        fixed: "right",
    },
]

export const columns = (isSelected = false) => {
    const d: TableColumn[] = [
        {
            prop: "select",
            type: "selection",
            selectable(row: any) {
                return ["审核未通过", "待审核"].includes(row["审核状态"])
            },
            hide: false,
        },
        ...baseColumns,
    ]
    if (isSelected) {
        d.unshift({ type: "selection", prop: "select" })
    }
    return d
}

export const columns2 = (isSelected = false) => {
    const d: TableColumn[] = [
        {
            prop: "selectV2",
            selectable(row: any) {
                return ["审核未通过", "待审核"].includes(row["审核状态"])
            },
            width: "120px",
        },
        ...baseColumns,
    ]
    if (isSelected) {
        d.unshift({ type: "selection", prop: "select" })
    }
    return d
}
