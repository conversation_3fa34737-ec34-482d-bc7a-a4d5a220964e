<template>
    <el-table-column
        v-if="!item.type || item.type === 'default'"
        v-bind="item"
        :show-overflow-tooltip="item.showOverflowTip"
        :key="item.prop"
        :align="item.align || 'center'"
    >
        <template slot="header" slot-scope="scope">
            <div>
                {{ scope.column.label }}
                <el-tooltip
                    :content="item.tips"
                    placement="top"
                    v-if="item.showTips"
                >
                    <i class="el-icon-info"></i>
                </el-tooltip>
            </div>
        </template>
        <template slot-scope="scope">
            <slot
                v-if="slots.includes(item.prop)"
                :name="item.prop"
                :row="scope.row"
                :index="scope.$index"
            />
            <v-node v-else :vNode="getVNode(item, scope.row)" />
        </template>
        <!-- item.children start -->
        <template v-if="item.children && item.children.length">
            <template v-for="child in item.children">
                <table-column-container
                    :key="child.prop"
                    :item="child"
                    :slots="slots"
                    :getVNode="getVNode"
                    v-if="!item.hide"
                >
                    <template v-for="slot in slots" #[slot]="scope">
                        <slot :name="slot" v-bind="scope" />
                    </template>
                </table-column-container>
            </template>
        </template>
        <!-- item.children end -->
    </el-table-column>
    <MyTableColumn
        v-else-if="item.type === 'selection'"
        v-bind="item"
        :key="item.prop"
        :align="item.align || 'center'"
        @selectionTypeChange="(e) => $emit('selectionTypeChange', e)"
    >
        <template slot="header" slot-scope="scope">
            <div>
                {{ scope.column.label }}
                <el-tooltip
                    :content="item.tips"
                    placement="top"
                    v-if="item.showTips"
                >
                    <i class="el-icon-info"></i>
                </el-tooltip>
            </div>
        </template>
    </MyTableColumn>
    <el-table-column
        v-else
        v-bind="item"
        :key="item.prop"
        :align="item.align || 'center'"
    >
        <template slot="header" slot-scope="scope">
            <div>
                {{ scope.column.label }}
                <el-tooltip
                    :content="item.tips"
                    placement="top"
                    v-if="item.showTips"
                >
                    <i class="el-icon-info"></i>
                </el-tooltip>
            </div>
        </template>
    </el-table-column>
</template>

<script lang='ts'>
    import { Component, Prop, Vue } from "vue-property-decorator"
    import { TableColumn } from "../table"
    import { keys } from "lodash"
    import MyTableColumn from "./my-table-column.vue"

    @Component({ name: "TableColumnContainer", components: { MyTableColumn } })
    export default class TableColumnContainer extends Vue {
        @Prop({ default: () => {} })
        private item!: TableColumn

        @Prop({ default: () => [] })
        private slots!: []

        @Prop({ default: () => (item: TableColumn, row: any) => {} })
        private getVNode!: Function
    }
</script>

<style lang='less'>
    @import "~@/css/variables.less";
    .handler-btn {
        color: @main-color;
        cursor: pointer;
        user-select: none;
        &:not(:first-child) {
            margin-left: 10px;
        }
    }
</style>
