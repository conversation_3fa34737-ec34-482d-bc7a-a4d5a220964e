<script lang='ts'>
    import { Component, Prop, Vue } from "vue-property-decorator"
    import { TableColumn } from "element-ui"
    import SelectionHeader from "./selection-header.vue"

    @Component({ components: { SelectionHeader }, extends: TableColumn })
    export default class MyTableColumn extends Vue {
        @Prop({ default: false })
        all!: boolean

        setColumnRenders(e: any) {
            const defaultRender = (
                TableColumn as any
            ).methods.setColumnRenders.call(this, e)
            const fn = defaultRender.renderHeader
            const h = this.$createElement
            const that = this
            defaultRender.renderHeader = function (...args: any) {
                const f = fn.call(this, ...args)
                return h(
                    SelectionHeader,
                    {
                        props: {
                            column: e,
                            all: that.all,
                        },
                        on: {
                            selectionTypeChange(e: any) {
                                that.$emit("selectionTypeChange", e)
                            },
                        },
                    },
                    [f]
                )
            }
            return defaultRender
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
</style>

