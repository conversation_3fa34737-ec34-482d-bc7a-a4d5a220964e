import { popupService } from "@/core-ui/component/element-ui/popup"
import { ExcelGenerator } from "./excel-generator"
import { find } from "lodash"
import { ListRow, metaRow } from "uniplat-sdk"
import { Component, Model, Vue, Ref } from "vue-property-decorator"
import { TableColumn, TableConfig } from "."
import { UICore } from "@/core-ui/service/setup"
import { metaFilter } from "uniplat-sdk/build/main/def"
import { getTextFromVNode } from "@/core-ui/helpers/tools"
import { CreateElement, VNode } from "vue"
import { SelectionType } from "../common-table"
export type RowsConverter = (
    rows: metaRow<ListRow>[] | []
) => (string | number)[][]

export interface ExportV2Params {
    rowsConverter?: RowsConverter
    columns?: string[]
    title?: string
}

export interface PageData<T> {
    item_index: number
    item_size: number
    name: string
    record_count: number
    rows: T[]
    filterData: Record<string, any> | null
}

export function tableDataToArray(
    d: any[],
    columns: TableColumn[],
    h: CreateElement
) {
    return _.map(d, (item) => {
        return _.map(columns, (i) => {
            if (i.formatter) {
                return i.formatter(item, i)
            }
            if (i.render) {
                const d = i.render(h, item, i) as VNode
                return getTextFromVNode(d)
            }
            return item[i.prop]
        })
    })
}
@Component({ components: {} })
export class BaseTable<T> extends Vue {
    protected index = 1
    protected pageSizes = [10, 20, 50, 100]
    public size = this.pageSizes[0]
    public total = 0
    protected loading = false

    public items: T[] = []

    protected pageDatas: PageData<T>[] = []
    protected currentPageName = ""

    protected showExportPop = false
    protected loadingMsg = {
        percent: 0,
        status: "",
        process: null as any,
    }

    @Model("update")
    protected readonly tableConfig!: TableConfig

    public getCurrentPage() {
        return find(this.pageDatas, { name: this.currentPageName })!
    }

    protected handleSizeChange(size: number) {
        const currentPage = this.getCurrentPage()
        if (this.currentPageName && currentPage) {
            currentPage.item_size = size
        } else {
            this.size = size
        }
        this.onFilterChanged()
    }

    public async handleCurrentChange(i: number) {
        const currentPage = this.getCurrentPage()
        if (this.currentPageName && currentPage) {
            currentPage.item_index = i
        } else {
            this.index = i
        }
        await this.onFilterChanged()
    }

    protected onFilterChanged() {
        // TODO
    }

    private defaultRowsConverter(row: metaRow<ListRow>[] | []) {
        const d = UICore.buildRows<{ [key: string]: string }>(
            row,
            this.tableConfig.predict!
        )
        return tableDataToArray(
            d,
            this.tableConfig.column!,
            this.$createElement
        )
    }

    /**
     *
     * @param params {rowsConverter: 格式化数据 columns:列 title 文件名}
     * @returns
     */
    public exportV2(params?: ExportV2Params) {
        if (!this.tableConfig.column) {
            return
        }
        return this.getDate4Export(
            params?.rowsConverter || this.defaultRowsConverter
        ).then((r) => {
            popupService.toast.success(`导出成功`)
            ExcelGenerator.execute({
                primaryRows: [],
                columns:
                    params?.columns ||
                    this.tableConfig.column!.map((i) => i.label),
                rows: r,
                fileName: params?.title || "导出",
            })
        })
    }

    private getDate4Export(rowsConverter: RowsConverter, index = 1) {
        if (!this.tableConfig.model) {
            return Promise.reject()
        }
        return this.tableConfig.model
            .query({
                pageIndex: index,
                item_size: 99,
            })
            .then(async (r) => {
                let d = rowsConverter(r.pageData.rows)
                if (
                    r.pageData.record_count >
                    r.pageData.item_size * (r.pageData.item_index + 1)
                ) {
                    const dx = await this.getDate4Export(
                        rowsConverter,
                        index + 1
                    )
                    d = d.concat(dx)
                }
                return d
            })
    }
}

@Component({})
export class BaseTableController<T extends { id: number }> extends Vue {
    protected row: T | null = null
    protected rowId = 0
    protected showDetail = false
    protected isEdit = false
    protected exportLoading = false

    @Ref()
    public table?: {
        total: number
        index: number
        size: number
        pageDatas: PageData<T>
        items: T
        onFilterChanged: (
            filterData?: Record<string, string>,
            preFilterData?: Record<string, string>
        ) => Promise<void>

        reload: (resetPage?: boolean) => void
        queryTab: (next?: boolean) => void
        exportToExcel: () => Promise<void>
        exportV2: (e?: ExportV2Params) => Promise<void>
        exportExcelUniplatV2: (e: {
            template_name: string
            file_name?: string
            data_type?: string
        }) => Promise<void>
        metaFilters: metaFilter[]
        setMetaFilters: (metaFilters: metaFilter[]) => void

        filter: {
            setFilterData: (filterData: Record<string, any>) => void
        }
        filters: {
            setFilterData: (filterData: Record<string, any>) => void
        }[]

        handleCurrentChange: (i: number) => Promise<void>
    }

    protected add() {
        this.row = null
        this.rowId = 0
        this.isEdit = true
        this.showDetail = true
    }

    protected viewDetail(e: T) {
        this.row = e
        this.rowId = e.id
        this.isEdit = false
        this.showDetail = true
    }

    public exportExcelUniplatV2(e: {
        template_name: string
        file_name?: string
        data_type?: string
    }) {
        this.exportLoading = true
        this.table?.exportExcelUniplatV2(e).finally(() => {
            this.exportLoading = false
        })
    }

    // 多用于一个列表
    public refreshList() {
        this.table?.onFilterChanged()
    }

    // 多用于有多个tab的；传false可实现刷新所有tab并且保持页码
    public reloadList(resetPage = true) {
        this.table?.reload(resetPage)
    }

    public getNextPages() {
        this.table?.queryTab(true)
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars, no-unused-vars
    protected setMetaFilters(metaFilters: metaFilter[]) {
        this.table?.setMetaFilters(metaFilters)
    }

    public exportToExcel() {
        this.exportLoading = true
        this.table?.exportToExcel().finally(() => {
            this.exportLoading = false
        })
    }

    public exportToExcelV2(e: ExportV2Params) {
        this.exportLoading = true
        this.table?.exportV2(e).finally(() => {
            this.exportLoading = false
        })
    }

    curSelectionType = SelectionType.本页
    get isSelectionCurPage() {
        return this.curSelectionType === SelectionType.本页
    }

    selectionTypeChange(type: SelectionType) {
        this.curSelectionType = type
    }

    currentQueryParams = null
    updateQueryParams(p: any) {
        this.currentQueryParams = p
    }
}
