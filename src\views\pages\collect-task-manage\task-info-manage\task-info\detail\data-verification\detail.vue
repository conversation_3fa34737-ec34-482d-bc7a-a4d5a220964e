<template>
    <div class="core-ui-table-container" :key="refreshQueryParams">
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
            <div class="u-flex">
                <el-button @click="handleRecollect">重新采集</el-button>
                <el-button
                    v-for="action in renderAction"
                    :key="action.action_name"
                    type="primary"
                    @click="handleAction(action)"
                >
                    {{ action.label }}
                </el-button>
            </div>
        </div>

        <detail-top :items="detailItems" :title="detailTitle" />

        <el-tabs v-model="currentPageName" @tab-click="onTabChange">
            <el-tab-pane
                v-for="(item, index) in tabs"
                :key="index"
                :label="item.label"
                :name="item.name"
                class="bg-white"
                lazy
            >
                <table-container
                    v-if="item.tableConfig"
                    ref="table"
                    :tableConfig="item.tableConfig"
                    @getData="onGetData"
                    @tabName="onTabName"
                >
                    <template slot="header-right">
                        <div class="u-p-x-20">
                            <el-button type="primary" @click="addCallOutTask"
                                >添加外呼任务</el-button
                            >
                        </div>
                    </template>
                    <div
                        slot="table"
                        slot-scope="{ data }"
                        class="u-p-20 bg-white"
                        :class="{
                            'custom-selector-table':
                                currentPageName === 'data_verify_source_data',
                        }"
                    >
                        <div class="select-each-num">
                            <el-checkbox
                                @click.stop.prevent
                                @focus.stop.prevent
                                class="table-select-chechbox"
                                :disabled="!tableSelectedVisible"
                                v-model="tableSelected"
                                :indeterminate="indeterminate"
                                @change="selectedChanged"
                            />
                            <el-select
                                ref="typeSelect"
                                v-model="selectType"
                                class="header-select"
                                popper-class="header-select-popper"
                                @change="selectTypeChange"
                                :title="selectTopTitle"
                            >
                                <el-option label="本页" :value="0" />
                                <el-option label="全部" :value="1" />
                                <el-option :label="topItemText" :value="2">
                                    前
                                    <input
                                        v-model="top"
                                        class="top-input"
                                        @click.stop
                                        @focus.stop
                                        @input="onInputChange"
                                    />
                                    条
                                </el-option>
                            </el-select>
                        </div>

                        <common-table
                            :data="getCurrentTableData(data, item.name)"
                            :columns="getTableColumns(item)"
                            @handleSelectionChange="handleSelectionChange"
                        >
                            <!-- 批量选择列 - 只在 data_verify_source_data 页面显示 -->
                            <template
                                v-if="
                                    currentPageName ===
                                    'data_verify_source_data'
                                "
                                slot="selectV2"
                                slot-scope="scope"
                            >
                                <el-checkbox
                                    @change="change($event, scope.row)"
                                    v-model="scope.row.selected"
                                    :disabled="scope.row.selectedDisabled"
                                ></el-checkbox>
                            </template>

                            <template #h="{ row }">
                                <el-button
                                    v-if="
                                        [
                                            'data_verify_source_data',
                                            'callout_task',
                                        ].includes(item.name)
                                    "
                                    type="text"
                                    @click="goDetail(row, item.name)"
                                >
                                    详情
                                </el-button>
                            </template>
                            <template #callOutH="{ row }">
                                <el-button
                                    v-if="
                                        [
                                            'data_verify_source_data',
                                            'callout_task',
                                        ].includes(item.name)
                                    "
                                    type="text"
                                    @click="goDetail(row, item.name)"
                                >
                                    详情
                                </el-button>
                            </template>
                        </common-table>
                    </div>
                </table-container>
            </el-tab-pane>
        </el-tabs>

        <action-container
            v-model="showActionContainer"
            ref="actionContainer"
            :action="action"
            :modelName="modelName"
            :selected_list="selected_list"
            :prefilters="prefilters"
            :filters="filters"
            @success="handleActionSuccess"
        />
    </div>
</template>

<script lang="ts">
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { TableConfig } from "@/core-ui/component/table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { buildConfig4RemoteMeta } from "@/views/common/list/build-table"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import {
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "@/views/pages/single-page/components/tags-view"
    import ActionContainer from "./action-container.vue"
    import { action, Meta, metaRow2 } from "uniplat-sdk"
    import { Component, Vue, Ref } from "vue-property-decorator"
    import { auditColumns, callOutColumns } from "./detail"
    import DetailTop from "./components/detail-top.vue"
    import { cloneDeep, without } from "lodash"

    enum SelectType {
        Page = 0,
        All = 1,
        Top = 2,
    }

    @Component({
        name: routesMap.collectTaskManage.taskInfoManage.dataVerification.detail,
        components: { TableContainer, CommonTable, ActionContainer, DetailTop },
    })
    export default class DataVerificationDetail extends Vue {
        private breadcrumbs: BreadcrumbItem[] = []

        refreshConfig = {
            fun: this.refresh,
            name: routesMap.collectTaskManage.taskInfoManage.dataVerification
                .detail,
        }

        private meta: Meta["meta"] | null = null
        private row: metaRow2 | null = null

        private refresh() {
            console.log("refresh")
        }

        // 批量选择相关计算属性
        private get selectTopTitle() {
            return this.selectType === SelectType.Top ? `前${this.top}条` : ""
        }

        private get topItemText() {
            if (this.top > 999) {
                return "前N条"
            }
            return `前${this.top}条`
        }

        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                ...getCacheBreadcrumbsByRoutePath(this.$route.query.from as string),
                {
                    label: "核验任务",
                    to: {
                        name: routesMap.collectTaskManage.taskInfoManage
                            .dataVerification.detail,
                        query: {
                            ...this.$route.query,
                        },
                    },
                },
            ]
            updateTagItem({
                name: routesMap.collectTaskManage.taskInfoManage.dataVerification
                    .detail,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        // tabs
        private currentPageName = ""
        private tabs: any[] = []

        // 批量选择相关属性
        private selectType = SelectType.Page
        private tableSelectedVisible = true
        private tableSelected = false
        private curData: any = []
        private indeterminate = false
        private checkIds: string[] = []
        private top = 100
        private pageDatas: any[] = []

        @Ref()
        private commonTable!: CommonTable

        private onTabChange(tab: any) {
            // 当切换到某个tab时，构建该tab的配置
            this.buildTabConfig(tab.name)

            // 重置批量选择状态
            this.resetBatchSelectState()
        }

        private resetBatchSelectState() {
            this.selectType = SelectType.Page
            this.tableSelectedVisible = true
            this.tableSelected = false
            this.curData = []
            this.indeterminate = false
            this.checkIds = []
            this.top = 100
            console.log("批量选择状态已重置")
        }

        private async buildTabConfig(tabName: string) {
            // 找到对应的tab
            const tab = this.tabs.find((t) => t.name === tabName)
            // 如果已经构建过配置，直接返回
            if (tab.tableConfig) {
                return
            }

            try {
                const { tableConfig, columns } = await this.createTableConfig(tab)
                console.log("tab", tab)
                let newColumns = columns
                if (tab.label === "核验信息") {
                    newColumns = auditColumns()
                }
                if (tab.label === "外呼任务") {
                    newColumns = callOutColumns()
                }
                this.$set(tab, "tableConfig", tableConfig)
                this.$set(tab, "columns", newColumns)
            } catch (error) {
                console.error(`构建Tab ${tabName} 配置失败:`, error)
            }
        }

        private onGetData(data: any) {
            // 表格数据接收处理
            console.log("onGetData", data)
        }

        private onTabName(tabName: string) {
            // Tab名称变化处理
            console.log("onTabName", tabName)
        }

        private async createTableConfig(
            page: any
        ): Promise<{ tableConfig: TableConfig; columns: any }> {
            try {
                const modelName = page.list.name
                const listName = page.list.list_name
                const prefilters = this.buildPreFilter(page.list.prefilters)

                const config: any = await buildConfig4RemoteMeta(
                    modelName,
                    listName,
                    {
                        prefilters,
                        useLabelWidth: true,
                        disabledOpt: false,
                        disabledFilter: false,
                    }
                )

                const tableConfig = config.tableConfig
                tableConfig.defaultPageSize = 10

                return {
                    tableConfig,
                    columns: config.columns || [],
                }
            } catch (error) {
                console.error("创建表格配置失败:", error)
                return {
                    tableConfig: {},
                    columns: [],
                }
            }
        }

        private buildPreFilter(prefilters: any[]): Record<string, any> {
            const preFilter: Record<string, any> = {}

            if (prefilters && prefilters.length > 0) {
                prefilters.forEach((filter: any) => {
                    preFilter[filter.property] = filter.value
                })
            }

            return preFilter
        }

        private goDetail(row: any, itemName: string) {
            if (itemName === "data_verify_source_data") {
                this.$router.push({
                    name: routesMap.collectTaskManage.taskInfoManage
                        .dataVerification.personDetail,
                    query: {
                        id: row._access_key,
                        from: this.$route.name,
                        model: itemName,
                    },
                })
            }
            if (itemName === "callout_task") {
                this.$router.push({
                    name: routesMap.collectTaskManage.taskInfoManage
                        .dataVerification.callOutDetail,
                    query: {
                        id: row.id,
                        from: this.$route.name,
                    },
                })
            }
        }

        private queryDetail() {
            sdk.core
                .model("data_verify_task")
                .detail2(this.$route.query.id as string, "manage")
                .query()
                .then((res) => {
                    console.log("res", res)

                    const {
                        meta: { pages, actions },
                        row,
                    } = res
                    this.meta = res.meta
                    this.row = res.row

                    this.currentPageName =
                        pages.find(
                            (item) => item.name === "data_verify_source_data"
                        )?.name || ""

                    this.tabs = pages
                        .filter(
                            (page) =>
                                ![
                                    "uniplat_model_remark",
                                    "uniplat_work_flow_log",
                                    "data_verify_task_data_ratio",
                                ].includes(page.name)
                        )
                        .map((page: any) => ({
                            ...page,
                            tableConfig: null, // 初始时不构建配置
                        }))

                    // 构建第一个tab的配置
                    this.buildTabConfig(this.currentPageName)
                })
                .catch((error) => {
                    console.error("查询详情失败:", error)
                })
        }

        get detailTitle() {
            return this.meta?.title_template || "--"
        }

        get detailItems() {
            return (
                this.meta?.header.field_groups.map((item) => {
                    return {
                        label: item.label,
                        value: item.template || "--",
                        span: item.span || 6,
                    }
                }) || []
            )
        }

        private action: action | null = null
        private modelName = "data_verify_task"
        private selected_list: { id: string; v: number }[] = []
        private prefilters: { property: string; value: string | number }[] = []
        private filters: Record<string, any[]> = {}

        private handleAction(action: action) {
            this.action = action
            this.selected_list = [
                {
                    id: this.row?.keyValue as string,
                    v: this.row?.uniplatVersion as number,
                },
            ]
            // this.prefilters = [
            //     {
            //         property: "collect_root_task_id",
            //         value: this.detailRow.root_task_id,
            //     },
            //     {
            //         property: "is_del",
            //         value: 0,
            //     },
            // ]

            this.$nextTick(() => {
                this.showActionContainer = true
            })
        }

        private showActionContainer = false
        private handleActionSuccess(res: any) {
            this.$message.success(res.msg || "操作成功")
            this.showActionContainer = false
            // this.refreshList()
        }

        get renderAction() {
            return this.meta?.actions
        }

        mounted() {
            this.setBreadcrumbs()
            this.queryDetail()

            // 初始化批量选择状态
            console.log("批量选择组件已挂载")
            console.log("selectType:", this.selectType)
            console.log("tableSelectedVisible:", this.tableSelectedVisible)
        }

        // 批量选择相关方法
        private getCurrentTableData(data: any[], itemName: string) {
            // 只在 data_verify_source_data 页面处理批量选择数据
            if (itemName !== "data_verify_source_data") {
                return data
            }

            // 如果有 curData，优先使用 curData（批量选择处理后的数据）
            if (this.curData && this.curData.length > 0) {
                return this.curData
            }

            // 否则处理原始数据并返回
            if (data && data.length > 0) {
                this.curData = this.handlerData(data)
                this.setAllSelectedStatus()
                console.log("处理表格数据:", this.curData)
                return this.curData
            }

            return data
        }

        private getTableColumns(item: any) {
            // 只在 data_verify_source_data 页面添加选择列
            if (item.name === "data_verify_source_data") {
                const columns = [...item.columns]
                // 在第一列添加选择列
                columns.unshift({
                    label: "",
                    prop: "selectV2",
                    width: "120px",
                    fixed: "left",
                })
                return columns
            }
            return item.columns
        }

        private handlerData(data: any[]) {
            return data.map((e) => {
                // 处理不同的数据结构，确保能获取到正确的 id
                const id = e.id?.value || e.id || e._access_key
                return {
                    ...e,
                    selected: this.checkIds.includes(id),
                    selectedDisabled: false,
                }
            })
        }

        private change(selected: any, row: any) {
            // 处理不同的数据结构，确保能获取到正确的 id
            const id = row.id?.value || row.id || row._access_key

            if (selected && !this.checkIds.includes(id)) {
                this.checkIds.push(id)
            } else if (!selected) {
                this.checkIds = without(this.checkIds, id)
            }
            this.setAllSelectedStatus()

            // 打印选中的数据到控制台
            console.log("当前选中的数据IDs:", this.checkIds)
            console.log(
                "当前选中的数据详情:",
                this.curData.filter((item: any) => item.selected)
            )
        }

        private onInputChange() {
            let num: any = this.top * 1

            if (isNaN(num)) {
                num = (this.top + "").replace(/[^\d]/g, "") || 0
            }

            if (num * 1 > 999) {
                this.top = 999
                this.selectTypeChange()
                return
            }
            this.top = num
            this.selectTypeChange()
        }

        private selectTypeChange() {
            this.tableSelected = false

            // 确保有数据可以操作
            if (!this.curData || this.curData.length === 0) {
                console.log("没有数据可以操作")
                return
            }

            if (this.selectType === SelectType.All) {
                this.tableSelectedVisible = false
                this.curData.forEach((item: any) => {
                    item.selected = false
                    item.selectedDisabled = true
                })
                // 打印全部选择状态
                console.log("选择类型：全部")
                console.log("全部数据将被选中（跨页面）")
                return
            }
            if (this.selectType === SelectType.Top) {
                this.tableSelectedVisible = false
                this.curData.forEach((item: any, index: number) => {
                    if (index < this.top) {
                        item.selected = true
                    } else {
                        item.selected = false
                    }
                    item.selectedDisabled = true
                })
                // 打印前N条选择状态
                console.log(`选择类型：前${this.top}条`)
                console.log(
                    "选中的数据:",
                    this.curData.filter((item: any) => item.selected)
                )
                return
            }
            this.curData.forEach((item: any) => {
                item.selected = false
                item.selectedDisabled = false
            })
            this.tableSelectedVisible = true
            console.log("选择类型：本页")

            // 强制更新视图
            this.$forceUpdate()
        }

        private selectedChanged(checked: boolean) {
            this.curData.forEach((item: any) => (item.selected = checked))
            this.checkIds = this.curData
                .filter((e: any) => e.selected)
                .map((e: any) => e.id?.value || e.id || e._access_key)

            this.setAllSelectedStatus()

            // 打印本页选择状态
            console.log("本页全选状态:", checked)
            console.log("当前选中的数据IDs:", this.checkIds)
            console.log(
                "当前选中的数据详情:",
                this.curData.filter((item: any) => item.selected)
            )
        }

        private setAllSelectedStatus() {
            const cLength = this.checkIds.length
            if (!cLength) {
                this.tableSelected = false
                this.indeterminate = false
            } else if (cLength === this.curData.length) {
                this.tableSelected = true
                this.indeterminate = false
            } else {
                this.tableSelected = false
                this.indeterminate = true
            }
        }

        private handleSelectionChange(d: { ids: string[]; rows: any[] }) {
            this.checkIds = d.rows.map((e) => e.id?.value || e.id || e._access_key)
            console.log("表格选择变化:", this.checkIds)
        }

        private addCallOutTask() {
            // 获取搜索参数
            const searchValues = this.getCurrentSearchValues()
            console.log("搜索参数:", searchValues)
        }

        private getCurrentSearchValues() {
            const tableRef = this.$refs.table as any
            console.log("tableRef", tableRef)

            if (tableRef && tableRef.$refs && tableRef.$refs.filters) {
                if (Array.isArray(tableRef.$refs.filters)) {
                    const currentPageIndex = this.pageDatas.findIndex(
                        (page: any) => page.name === this.currentPageName
                    )

                    if (
                        currentPageIndex >= 0 &&
                        tableRef.$refs.filters[currentPageIndex]
                    ) {
                        const filterData =
                            tableRef.$refs.filters[currentPageIndex].getFilterData()
                        return filterData
                    }
                } else {
                    const filterData =
                        tableRef.$refs.filters[0]?.getFilterData() || {}
                    return filterData
                }
            }
            return {}
        }

        private handleRecollect() {
            this.$router.push({
                name: routesMap.collectTaskManage.taskInfoManage.taskInfo.create,
                query: {
                    id: this.$route.query.task_detail_id as string,
                    from: this.$route.name,
                    edit: "1",
                },
            })
        }
    }
</script>

<style lang="less" scoped>
    .select-each-num {
        position: absolute;
        display: none;
        left: 20px;
        width: 120px;
        top: 32px;
        cursor: pointer;
        z-index: 99;
        height: 64px;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 5px;
    }

    .custom-selector-table {
        position: relative;

        .select-each-num {
            display: flex !important;
        }

        /deep/ table {
            thead {
                & > tr {
                    &:first-child {
                        & > th {
                            padding: 20px 0;

                            &:first-child {
                                .cell {
                                    position: relative;
                                    top: -10px;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    .header-select {
        /deep/ .el-input__suffix {
            display: none;
        }
        /deep/ .el-input__inner {
            padding: 0;
            height: 16px;
            line-height: 16px;
            border: none;
            background-color: transparent;
            width: 58px;
            text-align: center;
            font-size: 13px;
            color: #5780ab;
            border-bottom: 1px solid #ccc;
            border-radius: 0;
        }
        &.no-checkbox /deep/ .el-input__inner {
            width: 50px;
            text-align: center;
        }
    }

    /deep/ .el-table__row {
        .el-checkbox__input.is-checked .el-checkbox__inner {
            background-color: var(--primary, #0bcc27) !important;
            border-color: var(--primary, #0bcc27) !important;

            &::after {
                border-color: #fff !important;
            }
        }
    }

    .top-input {
        width: 50px;
        height: 22px;
        line-height: 20px;
        padding: 0 2px;
        text-align: center;
        border-top: none;
        border-left: none;
        border-right: none;
        border-radius: 0;
        background-color: transparent;
        border-bottom: #5780ab 1px solid;
        color: #5780ab;

        &:focus {
            outline: none;
        }
    }
</style>
