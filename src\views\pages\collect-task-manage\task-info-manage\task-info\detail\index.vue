<template>
    <div class="core-ui-table-container" :key="refreshQueryParams">
        <div class="core-ui-custom-header">
            <!-- {{ detailRow }} -->
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>

            <div class="d-flex">
                <el-button
                    class="ml-auto"
                    plain
                    @click="showImportPop = true"
                    v-if="
                        getPageViewsInDetail('collect_operator_log') &&
                        detailRow
                    "
                    >导入</el-button
                >

                <el-button
                    class="ml-auto"
                    type="primary"
                    plain
                    @click="goImportCenter"
                    v-if="getPageViewsInDetail('collect_operator_log')"
                    >导入中心</el-button
                >

                <el-button
                    class="ml-auto"
                    type="primary"
                    v-if="getViewActionDisplayInfo('open_task')"
                    @click="openStart('open_task')"
                    >发布</el-button
                >

                <el-button
                    class="ml-auto"
                    type="primary"
                    v-if="getViewActionDisplayInfo('pause_task')"
                    @click="openStart('pause_task')"
                    >任务暂停</el-button
                >

                <el-button
                    class="ml-auto"
                    type="primary"
                    v-if="getViewActionDisplayInfo('resume_task')"
                    @click="openStart('resume_task')"
                    >任务继续</el-button
                >

                <el-button
                    class="ml-auto"
                    type="primary"
                    v-if="getViewActionDisplayInfo('finish_task')"
                    @click="openStart('finish_task')"
                    >任务结束</el-button
                >
            </div>
        </div>
        <DetailTop :detailRow="detailRow" v-if="detailRow"></DetailTop>
        <div class="detail-content" v-if="detailRow">
            <!-- <div class="tree-box">
                <tree-view
                    v-if="domainServiceTree"
                    ref="treeView"
                    :treeTitle="treeTitle || '区域选择'"
                    :domainService="domainServiceTree"
                    :defaultExpandKeys="defaultExpandKeys"
                    :rootNode="curNodeId"
                    :auto="true"
                    @node-select-change="handleTreeViewClick"
                ></tree-view>
            </div> -->
            <div class="right-box">
                <div class="page-tab u-flex u-row-between">
                    <el-tabs v-model="currentPageName" @tab-click="onTabChange">
                        <el-tab-pane
                            v-for="(item, index) in tabs"
                            :key="index"
                            :label="item"
                            :name="item"
                        >
                        </el-tab-pane>
                    </el-tabs>
                </div>

                <div class="content">
                    <DetailList
                        ref="taskInfoDetailRef"
                        v-if="currentPageName === '任务总况'"
                        @setTarget="setTarget()"
                        @refresh="updateDetail"
                        :detailRow="detailRow"
                        :treeTitle="treeTitle"
                        :isInAllocation="isInAllocation"
                    ></DetailList>
                    <template v-if="!isClaimV2">
                        <TaskData
                            ref="taskInfoDetailRef"
                            v-if="currentPageName === '采集列表'"
                            :detailRow="detailRow"
                            :detail="detailRow"
                        ></TaskData>
                    </template>
                    <template v-else>
                        <TaskDataV2
                            ref="taskInfoDetailRef"
                            v-if="currentPageName === '采集列表'"
                            :detailRow="detailRow"
                            :detail="detailRow"
                        ></TaskDataV2>
                    </template>
                    <RecordList
                        ref="taskInfoDetailRef"
                        v-if="currentPageName === '信息审核'"
                        :detail="detailRow"
                    ></RecordList>
                    <template v-if="isClaimV2">
                        <ClaimV2
                            ref="taskInfoDetailRef"
                            v-if="currentPageName === '非本区域数据处理'"
                            :detailRow="detailRow"
                            :detail="detailRow"
                        ></ClaimV2>
                    </template>
                    <template v-else>
                        <Claim
                            ref="taskInfoDetailRef"
                            v-if="currentPageName === '非本区域数据处理'"
                            :detailRow="detailRow"
                            :detail="detailRow"
                        ></Claim>
                    </template>

                    <DataVerification
                        ref="taskInfoDetailRef"
                        v-if="currentPageName === '数据核验'"
                        :detailRow="detailRow"
                        :detail="detailRow"
                    ></DataVerification>
                </div>
            </div>
        </div>

        <DialogProcess
            @success="onProcessSuccess"
            :curProcessStatus="curProcessStatus"
            v-if="displayDialogProcess"
        ></DialogProcess>

        <ImportPop
            v-if="detailRow"
            v-model="showImportPop"
            :curId="detailRow.id"
            @reload="reloadImportList()"
        ></ImportPop>
    </div>
</template>

<script lang="ts">
    import { Component, Ref, Vue } from "vue-property-decorator"
    import TreeView from "../components/tree-view.vue"
    import { sdk } from "@/service"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import {
        CURRENT_REGION_NODE_LEVEL,
        CURRENT_TREE_REGION_NODE,
        DetailController,
    } from "../base"
    import { routesMap } from "@/router/direction"
    import {
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "@/views/pages/single-page/components/tags-view"
    import DetailRowCol from "@/views/components/detail-row-col/index.vue"
    import { AllocationStatus, DetailRow, Status } from "../model"
    import { MessageBox } from "element-ui"
    import { pageLoading } from "@/views/controller"
    import DetailTop from "./detail-top.vue"
    import DetailList from "./detail-list.vue"
    import TaskData from "./task-data.vue"
    import TaskDataV2 from "./task-data-v2.vue"
    import RecordList from "./record-list.vue"
    import Claim from "./claim.vue"
    import ClaimV2 from "./claim-v2.vue"
    import DialogProcess from "../components/dialog-process.vue"
    import { requestTreeData } from "../../../components/cache-tree"
    import ImportPop from "../import-center/import-pop.vue"
    import { config, EnvProject } from "@/config"
    import DataVerification from "./data-verification/index.vue"
    const CACHE_TREE = "CACHE_TREE"
    const CACHE_DETAIL_ID = "CACHE_DETAIL_ID"

    @Component({
        name: routesMap.collectTaskManage.taskInfoManage.taskInfo.detail,
        components: {
            DetailTop,
            DetailList,
            TaskData,
            TaskDataV2,
            RecordList,
            DetailRowCol,
            TreeView,
            DialogProcess,
            ImportPop,
            Claim,
            ClaimV2,
            DataVerification,
        },
    })
    export default class TaskInfoDetail extends Vue {
        @Ref("taskInfoDetailRef")
        private taskInfoDetailRef!: any

        private curNodeId = ""
        private defaultExpandKeys: any[] = []

        private treeTitle = ""

        // private curTitle = ""

        private breadcrumbs: BreadcrumbItem[] = []

        private tabs = ["任务总况", "采集列表", "信息审核", "数据核验"]

        private currentPageName = ""

        private showImportPop = false

        private detailId = ""
        private detailAccesskey = ""
        private detailLoading = true

        private displayDialogProcess = false
        private curProcessStatus = Status.待发布

        private detailRow: DetailRow | null = null
        private viewActions: DetailRow["actions"] | null = null

        private isClaimV2 = [
            EnvProject.红安项目,
            EnvProject.武汉数采项目,
            EnvProject.武穴项目,
            EnvProject.荆州项目,
        ].includes(config.envProject)

        private onTabChange() {
            if (!this.detailLoading) {
                this.$nextTick(() => {
                    this.noticeRegionIdUpdate()
                })
            }
        }

        refreshConfig = {
            fun: this.refresh,
            name: routesMap.collectTaskManage.taskInfoManage.taskInfo.detail,
        }

        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                ...getCacheBreadcrumbsByRoutePath(this.$route.query.from as string),
                {
                    label: "任务信息",
                    to: {
                        name: routesMap.collectTaskManage.taskInfoManage.taskInfo
                            .detail,
                        query: {
                            id: this.$route.query.id,
                            from: this.$route.query.from,
                        },
                    },
                },
            ]
            updateTagItem({
                name: routesMap.collectTaskManage.taskInfoManage.taskInfo.detail,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        private onProcessSuccess() {
            this.displayDialogProcess = false
            this.queryDetail(true, false)
        }

        private getViewDisplayInfo(key: string) {
            return DetailController.getPageViewsInDetail(key)
        }

        private timer = 0

        private checkDetail() {
            DetailController.getDetail(this.detailId, true).then((r) => {
                this.detailRow = r
                if (
                    this.detailRow?.allocation_status ===
                    AllocationStatus.分配任务执行中
                ) {
                    this.timer = setTimeout(() => {
                        this.checkDetail()
                    }, 2000)
                } else {
                    this.taskInfoDetailRef?.getDetail &&
                        this.taskInfoDetailRef?.getDetail()
                }
            })
        }

        destroyed() {
            clearTimeout(this.timer)
        }

        private get isInAllocation() {
            return (
                this.detailRow?.allocation_status ===
                AllocationStatus.分配任务执行中
            )
        }

        private reloadImportList() {
            this.callRefresh(
                routesMap.collectTaskManage.taskInfoManage.taskInfo.detailImport
            )
        }

        private queryDetail(force = false, setTreeStatus = false) {
            this.detailAccesskey = this.$route.query.id as string
            this.detailRow = null
            return DetailController.getDetail(this.detailAccesskey, force)
                .then((r) => {
                    console.log(r)
                    const displayLastTab = r.pages.find(
                        (i: any) => i.label === "非本区域数据认领"
                    )

                    this.tabs = this.tabs.filter((i) => i !== "非本区域数据处理")
                    if (displayLastTab) {
                        this.tabs.push("非本区域数据处理")
                    }

                    this.detailId = r.id
                    this.detailRow = r
                    this.curProcessStatus = r.status
                    if ([Status.分配中, Status.设置目标中].includes(r.status)) {
                        this.displayDialogProcess = true
                    }

                    if (
                        this.detailRow?.allocation_status ===
                        AllocationStatus.分配任务执行中
                    ) {
                        setTimeout(() => {
                            this.checkDetail()
                        }, 1000)
                    }

                    if (
                        setTreeStatus &&
                        this.detailRow &&
                        !sessionStorage.getItem(CURRENT_TREE_REGION_NODE)
                    ) {
                        this.curNodeId = this.detailRow.region_code + ""
                        const level = this.detailRow.region_level

                        if (force) {
                            if (!this.defaultExpandKeys.length) {
                                this.defaultExpandKeys = [this.curNodeId]
                            }
                        }

                        sessionStorage.setItem(
                            CURRENT_TREE_REGION_NODE,
                            this.curNodeId
                        )
                        sessionStorage.setItem(
                            CURRENT_REGION_NODE_LEVEL,
                            level + ""
                        )
                    }

                    this.currentPageName = this.tabs[0]

                    this.$nextTick(() => {
                        this.noticeRegionIdUpdate(true)
                    })
                    // this.initTree()
                })
                .finally(() => {
                    this.detailLoading = false
                })
        }

        private domainServiceTree: any = null

        mounted() {
            pageLoading(() => {
                this.setBreadcrumbs()
                return this.init(true)
            })
        }

        private refresh() {
            pageLoading(() => {
                clearTimeout(this.timer)
                return this.init(true)
            })
        }

        private init(force: boolean) {
            // this.initCacheTreeData()
            return this.queryDetail(force, true)
        }

        // private initCacheTreeData() {
        //     const cacheDetailAccesskey = sessionStorage.getItem(CACHE_DETAIL_ID)
        //     const curDetailAccesskey = this.$route.query.id
        //     if (cacheDetailAccesskey !== curDetailAccesskey) {
        //         // 如果当前存的queryID和缓存的不一致，则清空缓存数据
        //         sessionStorage.removeItem(CACHE_TREE)
        //         sessionStorage.removeItem(CURRENT_TREE_REGION_NODE)
        //         sessionStorage.removeItem(CURRENT_REGION_NODE_LEVEL)
        //         sessionStorage.setItem(CACHE_DETAIL_ID, curDetailAccesskey + "")
        //     }

        //     const data = this.getCacheValue()
        //     this.curNodeId = sessionStorage.getItem(CURRENT_TREE_REGION_NODE) || ""
        //     // this.curNodeId = this.cacheNodeId

        //     // ① 如果有缓存的值，且tree数据有，则直接走缓存的逻辑
        //     if (data.length && this.curNodeId) {
        //         const preNodes: any[] = []
        //         this.findTarget4Tree(data, this.curNodeId, preNodes)
        //         this.treeTitle =
        //             preNodes.map((i) => i.display).join("") || this.treeTitle
        //         if (preNodes.length) {
        //             this.curTitle =
        //                 preNodes[preNodes.length - 1].display || this.curTitle
        //             preNodes.pop()
        //         }
        //         this.defaultExpandKeys = preNodes.map((i) => i.id)
        //         if (!this.defaultExpandKeys.length) {
        //             this.defaultExpandKeys = [this.curNodeId]
        //         }
        //         return
        //     }

        //     this.defaultExpandKeys = []
        //     // ① 如果没有缓存的值，或者没有tree数据，则走默认的逻辑
        //     sessionStorage.removeItem(CURRENT_TREE_REGION_NODE)
        //     sessionStorage.removeItem(CURRENT_REGION_NODE_LEVEL)
        // }

        // private initTree() {
        //     this.domainServiceTree = (p: string) => {
        //         const treeCode = p
        //         const detailDefaultCode = this.detailRow?.region_code
        //         const curCode = treeCode || detailDefaultCode || ""

        //         // ① 直接将所有的丢进去，mounted有做是否是同一个id的限制
        //         // ② 从缓存里面拿，不请求数据
        //         // ③ 请求数据

        //         const data = this.getCacheValue()
        //         if (!treeCode && data.length) {
        //             this.setTreeTitle(this.curNodeId)
        //             return Promise.resolve(data)
        //         } else {
        //             const r = this.findTarget4Tree(data, curCode)
        //             if (r && r.children?.length) {
        //                 this.setTreeTitle(this.curNodeId)
        //                 return Promise.resolve(r.children)
        //             }
        //         }

        //         return requestTreeData("region_code", curCode, 5).then(
        //             (res: any) => {
        //                 const childrenNodes = this.handleData(res.children || [])
        //                 if (!p) {
        //                     res.current.children = childrenNodes
        //                     const currentNode = this.handleData([res.current])[0]

        //                     this.setCacheValue(p, [currentNode])
        //                     this.setCacheValue(curCode, childrenNodes)
        //                     this.setTreeTitle(currentNode.id)
        //                     return [currentNode]
        //                 }
        //                 this.setCacheValue(curCode, childrenNodes)
        //                 this.setTreeTitle(parent)
        //                 return childrenNodes
        //             }
        //         )
        //     }
        // }

        private setTarget() {
            MessageBox.confirm("确认自动设置目标？", "确认").then(() => {
                const model = sdk.core
                    .model("collect_task")
                    .action("auto_set_target_behavior")
                return pageLoading(() => {
                    return model
                        .updateInitialParams({
                            selected_list: [
                                {
                                    id: this.detailId,
                                    v: 0,
                                },
                            ],
                        })
                        .execute()
                        .then(() => {
                            this.$message.success("提交成功")
                            this.queryDetail(true, false)
                        })
                })
            })
        }

        private updateDetail() {
            this.queryDetail(true, false)
        }

        private handleData(treeData: any) {
            return treeData.map((i: any) => {
                const shortId = i.id
                const id = i.data.region_code
                return {
                    ...i,
                    shortId: shortId,
                    id: id,
                }
            })
        }

        private getViewActionDisplayInfo(key: string) {
            return DetailController.getBtnViewsInDetail(key)
        }

        private getPageViewsInDetail(key: string) {
            return DetailController.getPageViewsInDetail(key)
        }

        private openStart(
            type: "open_task" | "pause_task" | "resume_task" | "finish_task"
        ) {
            const obj = {
                open_task: "发布",
                pause_task: "暂停",
                resume_task: "继续",
                finish_task: "完成",
            }
            const optType = obj[type]
            MessageBox.confirm(`确定${optType}当前任务`, "确定").then(() => {
                pageLoading(() => {
                    return sdk.core
                        .model("collect_task")
                        .action(type)
                        .updateInitialParams({
                            selected_list: [
                                {
                                    id: this.detailId + "",
                                    v: 0,
                                },
                            ],
                        })
                        .execute()
                        .then(() => {
                            this.$message.success(`${optType}成功！`)
                            this.queryDetail(true, false)
                            this.callRefresh(this.$route.query.from as string)
                        })
                })
            })
        }

        private goImportCenter() {
            if (!this.detailRow) {
                return
            }
            this.$router.push({
                name: routesMap.collectTaskManage.taskInfoManage.taskInfo
                    .detailImport,
                query: {
                    id: this.detailRow.id + "",
                    from: this.$route.name,
                },
            })
        }

        // private setCacheValue(parent: string | number, result: any) {
        //     let data = this.getCacheValue()
        //     if (!parent) {
        //         data = result
        //     } else {
        //         const r = this.findTarget4Tree(data, parent)
        //         r && (r.children = result)
        //     }
        //     sessionStorage.setItem(CACHE_TREE, JSON.stringify(data))
        // }

        // private getPreNodes(node: string | number) {
        //     const data = this.getCacheValue()
        //     if (!node) {
        //         if (data) {
        //             return [data[0]]
        //         }
        //         return []
        //     } else {
        //         const preNodes: any[] = []
        //         this.findTarget4Tree(data, node, preNodes)
        //         return preNodes
        //     }
        // }

        // private setTreeTitle(node: any) {
        //     console.log("setTreeTitle")
        //     const preNodes = this.getPreNodes(node) || []
        //     this.treeTitle =
        //         preNodes.map((i: any) => i.display).join("") || this.treeTitle

        //     if (preNodes.length) {
        //         this.curTitle =
        //             preNodes[preNodes.length - 1].display || this.curTitle
        //     }
        //     console.log(this.treeTitle)
        // }

        // private getCacheValue() {
        //     return JSON.parse(sessionStorage.getItem(CACHE_TREE) || "[]")
        // }

        // private findTarget4Tree(
        //     data: { id: string; children: any[] }[],
        //     tId: string | number,
        //     preNodes?: any[]
        // ) {
        //     let res: any = ""
        //     const innerArr: any[] = preNodes || []
        //     ;(data || []).forEach((i) => {
        //         if (i.id + "" !== tId + "") {
        //             const target = this.findTarget4Tree(
        //                 i.children || [],
        //                 tId,
        //                 innerArr
        //             ) as any
        //             if (target) {
        //                 res = target
        //                 innerArr && innerArr.unshift(i)
        //             }
        //         } else {
        //             res = i
        //             innerArr && innerArr.unshift(i)
        //         }
        //     })
        //     return res
        // }

        // private handleTreeViewClick(value: any) {
        //     this.curNodeId = value[0].id
        //     sessionStorage.setItem(CURRENT_TREE_REGION_NODE, this.curNodeId + "")
        //     sessionStorage.setItem(
        //         CURRENT_REGION_NODE_LEVEL,
        //         value[0].data.region_grade + ""
        //     )
        //     this.setTreeTitle(this.curNodeId)

        //     this.noticeRegionIdUpdate()
        // }

        private noticeRegionIdUpdate(init = false) {
            if (
                this.taskInfoDetailRef &&
                this.taskInfoDetailRef.noticeRegionIdUpdate
            ) {
                this.taskInfoDetailRef.noticeRegionIdUpdate(init)
            }
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";

    .detail-content {
        // display: flex;
        // gap: 20px;

        .tree-box {
            background-color: #fff;
            width: 250px;
            flex: none;
        }

        .right-box {
            min-width: 912px;
            // margin-left: 10px;
            flex: 1;
        }

        .right-box-title {
            margin-bottom: 20px;
        }

        .content {
            background-color: #fff;
        }
    }

    // 自定义tab样式
    .page-tab {
        ::v-deep .el-tabs__item {
            min-width: auto !important;
            padding-inline: 20px !important;
        }
    }
</style>
