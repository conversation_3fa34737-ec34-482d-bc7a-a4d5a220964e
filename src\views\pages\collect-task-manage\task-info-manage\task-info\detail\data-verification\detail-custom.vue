<template>
    <div class="core-ui-table-container" :key="refreshQueryParams">
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
            <div class="u-flex">
                <el-button
                    v-for="action in renderAction"
                    :key="action.action_name"
                    type="primary"
                    @click="handleAction(action)"
                >
                    {{ action.label }}
                </el-button>
            </div>
        </div>

        <el-tabs v-model="currentPageName" @tab-click="onTabChange">
            <el-tab-pane
                v-for="(item, index) in tabs"
                :key="index"
                :label="item.label"
                :name="item.name"
                class="bg-white"
                lazy
            >
                <table-container
                    v-if="item.tableConfig"
                    :tableConfig="item.tableConfig"
                    @getData="onGetData"
                    @tabName="onTabName"
                >
                    <!-- <div slot="header-right" class="u-p-x-20">
                        <el-button type="primary"> 新增 </el-button>
                    </div> -->

                    <div
                        slot="table"
                        slot-scope="{ data }"
                        class="u-p-20 bg-white"
                    >
                        <common-table :data="data" :columns="item.columns">
                            <template #h="{ row }">
                                <el-button
                                    v-if="
                                        [
                                            'data_verify_source_data',
                                            'callout_task',
                                        ].includes(item.name)
                                    "
                                    type="text"
                                    @click="goDetail(row, item.name)"
                                >
                                    详情
                                </el-button>
                            </template>
                        </common-table>
                    </div>
                </table-container>
            </el-tab-pane>
        </el-tabs>

        <action-container
            v-model="showActionContainer"
            ref="actionContainer"
            :action="action"
            :modelName="modelName"
            :selected_list="selected_list"
            :prefilters="prefilters"
            :filters="filters"
            @success="handleActionSuccess"
        />
    </div>
</template>

<script lang="ts">
import CommonTable from "@/core-ui/component/common-table/index.vue"
import { TableConfig } from "@/core-ui/component/table"
import TableContainer from "@/core-ui/component/table/container.vue"
import { routesMap } from "@/router/direction"
import { sdk } from "@/service"
import { buildConfig4RemoteMeta } from "@/views/common/list/build-table"
import { BreadcrumbItem } from "@/views/components/breadcrumb"
import {
    getCacheBreadcrumbsByRoutePath,
    updateTagItem,
} from "@/views/pages/single-page/components/tags-view"
import ActionContainer from "./action-container.vue"
import { action, Meta, metaRow2 } from "uniplat-sdk"
import { Component, Vue } from "vue-property-decorator"

@Component({
    name: routesMap.collectTaskManage.taskInfoManage.dataVerification.detail,
    components: { TableContainer, CommonTable, ActionContainer },
})
export default class DataVerificationDetail extends Vue {
    private breadcrumbs: BreadcrumbItem[] = []

    refreshConfig = {
        fun: this.refresh,
        name: routesMap.collectTaskManage.taskInfoManage.dataVerification
            .detail,
    }

    private meta: Meta["meta"] | null = null
    private row: metaRow2 | null = null

    private refresh() {
        console.log("refresh")
    }

    private setBreadcrumbs() {
        const d: BreadcrumbItem[] = [
            ...getCacheBreadcrumbsByRoutePath(this.$route.query.from as string),
            {
                label: "核验任务",
                to: {
                    name: routesMap.collectTaskManage.taskInfoManage
                        .dataVerification.detail,
                    query: {
                        id: this.$route.query.id,
                        from: this.$route.query.from,
                    },
                },
            },
        ]
        updateTagItem({
            name: routesMap.collectTaskManage.taskInfoManage.dataVerification
                .detail,
            breadcrumb: d,
        })
        this.breadcrumbs = d
    }

    // tabs
    private currentPageName = ""
    private tabs: any[] = []

    private onTabChange(tab: any) {
        // 当切换到某个tab时，构建该tab的配置
        this.buildTabConfig(tab.name)
    }

    private async buildTabConfig(tabName: string) {
        // 找到对应的tab
        const tab = this.tabs.find((t) => t.name === tabName)
        // 如果已经构建过配置，直接返回
        if (tab.tableConfig) {
            return
        }

        try {
            const { tableConfig, columns } = await this.createTableConfig(tab)
            console.log("tab", tab)

            if (tab.label === "核验信息") {
                const info = columns.find(
                    (col: { prop: string }) => col.prop === "核验信息"
                )
                info.formatter = (h: any, row: any) => {
                    // 自定义格式化逻辑
                    return h("div", {
                        domProps: { innerHTML: row["核验信息"] },
                    })
                }
            }
            this.$set(tab, "tableConfig", tableConfig)
            this.$set(tab, "columns", columns)
        } catch (error) {
            console.error(`构建Tab ${tabName} 配置失败:`, error)
        }
    }

    private onGetData(data: any) {
        // 表格数据接收处理
        console.log("onGetData", data)
    }

    private onTabName(tabName: string) {
        // Tab名称变化处理
        console.log("onTabName", tabName)
    }

    private async createTableConfig(
        page: any
    ): Promise<{ tableConfig: TableConfig; columns: any }> {
        try {
            // 正确处理模型名称（处理@符号）
            const modelName = page.list.name
            const listName = page.list.list_name
            const prefilters = this.buildPreFilter(page.list.prefilters)

            const config: any = await buildConfig4RemoteMeta(
                modelName,
                listName,
                {
                    prefilters,
                    useLabelWidth: true,
                    disabledOpt: false,
                    disabledFilter: false,
                }
            )

            const tableConfig = config.tableConfig
            tableConfig.defaultPageSize = 10

            return {
                tableConfig,
                columns: config.columns || [],
            }
        } catch (error) {
            console.error("创建表格配置失败:", error)
            return {
                tableConfig: {},
                columns: [],
            }
        }
    }

    private buildPreFilter(prefilters: any[]): Record<string, any> {
        const preFilter: Record<string, any> = {}

        if (prefilters && prefilters.length > 0) {
            prefilters.forEach((filter: any) => {
                preFilter[filter.property] = filter.value
            })
        }

        return preFilter
    }

    private goDetail(row: any, itemName: string) {
        if (itemName === "data_verify_source_data") {
            this.$router.push({
                name: routesMap.collectTaskManage.taskInfoManage
                    .dataVerification.personDetail,
                query: {
                    id: row._access_key,
                    from: this.$route.name,
                    model: itemName,
                },
            })
        }
        if (itemName === "callout_task") {
            this.$router.push({
                name: routesMap.collectTaskManage.taskInfoManage
                    .dataVerification.callOutDetail,
                query: {
                    id: row.id,
                    from: this.$route.name,
                },
            })
        }
    }

    private queryDetail() {
        sdk.core
            .model("data_verify_task")
            .detail2(this.$route.query.id as string, "manage")
            .query()
            .then((res) => {
                const {
                    meta: { pages, actions },
                    row,
                } = res
                this.meta = res.meta
                this.row = res.row

                this.currentPageName =
                    pages.find(
                        (item) => item.name === "data_verify_source_data"
                    )?.name || ""

                this.tabs = pages
                    .filter(
                        (page) =>
                            ![
                                "uniplat_model_remark",
                                "uniplat_work_flow_log",
                                "data_verify_task_data_ratio",
                            ].includes(page.name)
                    )
                    .map((page: any) => ({
                        ...page,
                        tableConfig: null, // 初始时不构建配置
                    }))

                // 构建第一个tab的配置
                this.buildTabConfig(this.currentPageName)
            })
            .catch((error) => {
                console.error("查询详情失败:", error)
            })
    }

    private action: action | null = null
    private modelName = "data_verify_task"
    private selected_list: { id: string; v: number }[] = []
    private prefilters: { property: string; value: string | number }[] = []
    private filters: Record<string, any[]> = {}

    private handleAction(action: action) {
        this.action = action
        this.selected_list = [
            {
                id: this.row?.keyValue as string,
                v: this.row?.uniplatVersion as number,
            },
        ]
        // this.prefilters = [
        //     {
        //         property: "collect_root_task_id",
        //         value: this.detailRow.root_task_id,
        //     },
        //     {
        //         property: "is_del",
        //         value: 0,
        //     },
        // ]

        this.$nextTick(() => {
            this.showActionContainer = true
        })
    }

    private showActionContainer = false
    private handleActionSuccess(res: any) {
        this.$message.success(res.msg || "操作成功")
        this.showActionContainer = false
        // this.refreshList()
    }

    get renderAction() {
        return this.meta?.actions
    }

    mounted() {
        this.setBreadcrumbs()
        this.queryDetail()
    }
}
</script>

<style lang="less" scoped></style>
