import { TableColumn as ElTableColumn } from "element-ui"
import { isNumber } from "lodash"
import {
    DomainService,
    ListEasy,
    ListTypes,
    Method,
    prefilters,
} from "uniplat-sdk"
import { CreateElement, VNode, VNodeChildren } from "vue"
import { FormItem } from "@/core-ui/component/form"
export const TEXTWIDTH = 14
export type TableColumn<T = any> = Partial<
    Omit<ElTableColumn, "formatter" | "prop" | "render">
> & {
    prop: string
    property?: string
    formatter?: (row: T, column?: TableColumn<T>) => string | number
    renderTag?: string
    render?: (
        h: CreateElement,
        row: T,
        column?: TableColumn<T>
    ) => VNodeChildren | VNode
    emptyValue?: any[]
    emptyText?: string | number
    showOverflowTip?: boolean
    tips?: string
    showTips?: boolean
    children?: TableColumn<T>[]
    hide?: boolean
}

export interface TableFilter extends FormItem {
    /**
     * useTag 只针对 type = FormType.Select有效
     * 多个(也可以一个)：tagName1,tagName2;
     * 全部：*
     * 排除：*,!tagName1,!tagName2
     */
    useTag?: string
    // 多tab 动态显隐
    tabVisible?: string[]
    // 是否单独展示出来
    oneSelf?: boolean
    keyValueFilter?: Partial<ListTypes.KeyValueFilters[number]>
}

export interface TableConfig {
    model?: ListEasy
    domainService?: DomainService
    domainType?: "get" | "post"
    predict?: Record<string, string | boolean | number>
    defaultPageSize?: number
    filter?: TableFilter[]
    useRemoteFilter?: boolean
    outFilter?: TableFilter[]
    tabPages?: string[]
    defaultPage?: string
    oneTab?: boolean
    oneTabFilter?: boolean
    column?: TableColumn[]
    preFilter?: Record<string, any>
    handleFilterData?: (
        params: Record<string, any>
    ) => Record<string, any> & { preFilter?: prefilters }
    handlePreFilterData?: (
        params: Record<string, any>,
        currentPage: string
    ) => Record<string, any>
}

export function getCellvalue(
    row: Record<string, string>,
    column: TableColumn,
    emptyValue?: string | number | Array<string | number>
) {
    let value: string | number = column.property
        ? row[column.property]
        : row[column.prop]
    if (column.formatter) {
        value = column.formatter(row, column)
    }
    if (!emptyValue) {
        return value
    }
    if (isNumber(emptyValue)) {
        return emptyValue === value ? "" : value
    }
    return emptyValue.includes(value + "") ? "" : value
}

export function getCellDispaly(
    row: Record<string, string>,
    column: TableColumn,
    emptyText = "暂无",
    emptyValue?: string | number | Array<string | number>
) {
    return getCellvalue(row, column, emptyValue) || emptyText
}

export function getStatusLabel(
    h: CreateElement,
    rowObj: {
        label: string
        value: string
    },
    colorMap: any = {}
) {
    return h(
        "div",
        {
            class: `status-tag ${rowObj.value}`,
            style: {
                backgroundColor: colorMap[rowObj.value],
            },
        },
        rowObj.label
    )
}

export const exportDataTypeMap = new Map<string, string>([
    ["task_serve_record-for_wx_operate", "服务记录列表-企微服务"],
    [
        "job_fair_agent_apply-in_job_fair_detail_for_operate",
        "招聘会管理详情-报名企业",
    ],
    [
        "job_fair_agent_position-in_job_fair_detail_for_operate",
        "招聘会管理详情-报名岗位",
    ],
    [
        "job_fair_user_apply-in_job_fair_detail_for_operate",
        "招聘会管理详情-参会人员",
    ],
    [
        "xg_candidate_order-in_job_fair_detail_for_operate",
        "招聘会管理详情-投递信息",
    ],
        [
        "user_profile_start_job-list_start_intent",
        "创业意愿收集",
    ],
])
