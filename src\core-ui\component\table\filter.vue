<template>
    <div v-if="filterData" class="filter-container-out">
        <div
            class="filter-container-form one-self-containter"
            v-if="outFilterItems.length"
        >
            <template v-for="item in outFilterItems">
                <div
                    :key="item.prop"
                    class="item one-self"
                    v-if="!item.hide"
                    :style="[{ width: '100%' }, item.itemStyle]"
                >
                    <div
                        class="label"
                        :style="{
                            width: oneSelfWidth,
                        }"
                    >
                        {{ item.label }}
                    </div>
                    <div class="field">
                        <v-filed
                            v-bind="item"
                            :isEdit="true"
                            v-model="filterData[item.prop]"
                            :style="[{ width: '100%' }, item.filedStyle]"
                            :inFilter="true"
                            @input="outFilterInput(item)"
                            @keyupEnter="search()"
                        />
                    </div>
                </div>
            </template>
        </div>
        <div class="filter-container" v-if="filterItems.length">
            <div>
                <div class="filter-container-form">
                    <template v-for="item in filterItems">
                        <div
                            :key="item.prop"
                            class="item advice"
                            v-if="!item.hide"
                            :style="item.itemStyle"
                        >
                            <div
                                class="label"
                                :style="{
                                    width: width,
                                }"
                                :class="{ require: isRequire(item) }"
                            >
                                {{ item.label }}
                            </div>
                            <div class="field">
                                <v-filed
                                    v-bind="item"
                                    :isEdit="true"
                                    v-model="filterData[item.prop]"
                                    :style="[
                                        { width: filedWidth + 'px' },
                                        item.filedStyle,
                                    ]"
                                    :inFilter="true"
                                    @input="changeData"
                                    @changeRow="changeData"
                                    @keyupEnter="search()"
                                />
                            </div>
                        </div>
                    </template>

                    <!-- 占位 -->
                    <div
                        class="item advice end flex-fill d-flex-item-center justify-content-end"
                    >
                        <el-button
                            @click="reset"
                            plain
                            size="default"
                            v-if="!customSearch"
                        >
                            重置
                        </el-button>
                        <el-button
                            size="default"
                            type="primary"
                            @click="search()"
                        >
                            查询
                        </el-button>
                        <slot name="btn-right" />
                    </div>
                </div>
            </div>
        </div>
        <div v-if="showExpand" @click="expand = !expand" class="expand">
            {{ expand ? "收起" : "展开" }}筛选
            <i
                class="icon"
                :class="'el-icon-d-arrow-' + (expand ? 'left' : 'right')"
            ></i>
        </div>
    </div>
</template>

<script lang="ts">
    import {
        cloneDeep,
        filter,
        find,
        findIndex,
        forEach,
        get,
        map,
        max,
        some,
        take,
    } from "lodash"
    import { metaFilter, TagManagerTypes } from "uniplat-sdk/build/main/def"
    import { Component, Prop, ProvideReactive, Vue } from "vue-property-decorator"
    import { TableFilter, TEXTWIDTH } from "."
    import { buildFormItems, FormType, formType } from "@/core-ui/component/form"

    @Component({
        inject: [],
        components: {
            VFiled: () => import("@/core-ui/component/form/filed/index.vue"),
        },
    })
    export default class TableFilterComponent extends Vue {
        private filterData: Record<string, any> | null = null
        private filterDataCopy: Record<string, any> = {}
        private formType = formType
        private width = ""
        private oneSelfWidth = ""
        @Prop({ default: () => [] })
        private tableFilter!: TableFilter[]

        @Prop({ default: () => [] })
        private outFilter!: TableFilter[]

        @Prop({ default: 180 })
        private filedWidth!: number[]

        @Prop({ default: true })
        private defaultFilterIsExpand!: boolean

        @Prop({ default: false })
        private showExpand!: boolean

        @Prop({ default: "" })
        private currentPageName!: string

        public expand = true

        @Prop()
        private readonly metaFilters?: metaFilter[]

        @Prop()
        private readonly tagGroups!: TagManagerTypes.TagGroup[]

        @Prop({ default: null })
        handlerFilter?: any

        /** 自定义点击查询按钮的操作 */
        @Prop({ default: false })
        private readonly customSearch?: boolean

        @ProvideReactive()
        get formCompData() {
            return this.filterData
        }

        created() {
            this.expand = this.defaultFilterIsExpand
            this.width =
                max([
                    ...map(
                        this.tableFilter,
                        (i) => (i.label || "").length * TEXTWIDTH
                    ),
                ]) + "px"
            this.oneSelfWidth =
                max([
                    ...map(
                        this.outFilter,
                        (i) => (i.label || "").length * TEXTWIDTH
                    ),
                ]) + "px"
            const filterData: Record<string, string | number | boolean> = {}
            forEach([...this.tableFilter, ...this.outFilter], (item) => {
                filterData[item.prop] = item.defaultValue || ""
            })
            this.filterDataCopy = { ...filterData }
            this.filterData = filterData
        }

        private buildFilterItems(tableFilter: TableFilter[]) {
            return map(
                buildFormItems<TableFilter>(cloneDeep(tableFilter)),
                (item) => {
                    if (!item.sourceInputsParameter) {
                        const sourceFilter = find(this.metaFilters, (e: any) => {
                            return (
                                e.full_property === item.prop ||
                                [item.useOtherProp, item.prop].includes(e.property)
                            )
                        })
                        item.sourceInputsParameter = sourceFilter
                    }
                    const mapping_values = get(
                        item,
                        "sourceInputsParameter.ext_properties.mapping.mapping_values",
                        []
                    )
                    if (
                        item.useTag &&
                        (!item.sourceInputsParameter || !mapping_values.length)
                    ) {
                        const groups = this.filterTags(this.tagGroups, item.useTag)
                        // TODO 解析useTag 解析this.tagGroups 并 组装 item.sourceInputsParameter
                        let mapping_values: any[] = []
                        if (groups.length === 1) {
                            mapping_values = map(
                                find(this.tagGroups, (i) => {
                                    return [i.tagGroupName, "*"].includes(
                                        item.useTag!.trim()
                                    )
                                })?.tags,
                                (i) => {
                                    return { key: i.tagName, value: i.tagName }
                                }
                            )
                        } else {
                            mapping_values = map(groups, (t) => {
                                return {
                                    key: t.tagGroupName,
                                    value: `${t.tagGroupName}`,
                                    children: this.buildMappingValue(t),
                                }
                            })
                        }
                        item.sourceInputsParameter = {
                            ext_properties: {
                                mapping: {
                                    mapping_values,
                                },
                            },
                        }
                    }
                    if (item.tabVisible?.length && this.currentPageName) {
                        if (item.tabVisible.indexOf(this.currentPageName) < 0) {
                            item.hide = true
                        }
                    }
                    return item
                }
            )
        }

        private buildMappingValue(t: TagManagerTypes.TagGroup) {
            return map(t.tags, (i) => {
                return {
                    key: `${i.tagGroupName}:::${i.tagName}`,
                    value: i.tagName,
                }
            })
        }

        private filterTags(tagGroups: TagManagerTypes.TagGroup[], useTag: string) {
            let filteredTags: TagManagerTypes.TagGroup[] = []
            const tArr = useTag.split(",")
            if (useTag.includes("*")) {
                filteredTags = [...tagGroups]
            } else {
                const matchedTags =
                    filter(tagGroups, (tag) => {
                        return some(tArr, (t) => t === tag.tagGroupName)
                    }) || []
                filteredTags = [...matchedTags]
            }

            if (useTag.length && useTag.includes("!")) {
                const excludeConditions = filter(tArr, (i) => i.includes("!"))
                forEach(excludeConditions, (condition) => {
                    const tagName = condition.substring(1)
                    const index = findIndex(
                        filteredTags,
                        (tag) => tag.tagGroupName === tagName
                    )
                    if (index !== -1) {
                        filteredTags.splice(index, 1)
                    }
                })
            }

            return filteredTags
        }

        private get filterItems() {
            const arr = this.buildFilterItems(
                this.expand ? this.tableFilter : take(this.tableFilter, 3)
            )
            if (this.handlerFilter) {
                return this.handlerFilter(arr)
            }
            return arr
        }

        private get outFilterItems() {
            return this.buildFilterItems(this.outFilter)
        }

        private reset() {
            this.filterData = { ...this.filterDataCopy }
            this.search(true)
        }

        public setFilterData(filterData: Record<string, any>) {
            this.filterData = { ...filterData }
            this.search()
        }

        private outFilterInput(item: TableFilter) {
            if (item.type === FormType.Select2) {
                this.$nextTick(() => {
                    this.search()
                })
            }
            this.changeData()
        }

        public search(reset?: boolean) {
            this.$emit("search", this.filterData, reset)
        }

        public changeData() {
            this.$emit("changeData", this.filterData)
        }

        public getFilterData() {
            return this.filterData
        }

        isRequire(item: TableFilter) {
            return some(item.rules, (e) => e.required)
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .filter-container-out {
        position: relative;
        margin-bottom: 20px;
        .expand {
            color: #5782ec;
            cursor: pointer;
            margin: auto;
            width: 100px;
            height: 20px;
            background-color: #fff;
            text-align: center;
            border-radius: 0 0 4px 4px;
            // position: absolute;
            // top: -30px;
            // right: 10px;
            // z-index: 1;
            .icon {
                transform: rotate(90deg);
            }
        }
    }
    .filter-container {
        background: #fff;
        // box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.06);
        padding: 20px;
        padding-bottom: 3px;
        font-size: 14px;
        color: @text-primary-header;
        display: flex;
    }
    .one-self-containter {
        padding-bottom: 10px;
    }

    .filter-container-form {
        display: flex;
        flex-wrap: wrap;
        .item {
            min-height: 40px;
            display: flex;
            &.advice {
                margin-right: 20px;
                margin-bottom: 20px;
                .label {
                    color: #333;
                }
            }
            &.one-self {
                margin-right: 10px;
                .label {
                    color: #666;
                }
                & + .one-self {
                    margin-top: 1px;
                }
            }
            .label {
                line-height: 40px;
                flex-shrink: 0;
                text-align: right;
                position: relative;
                &.require::before {
                    position: absolute;
                    content: "*";
                    left: -8px;
                    top: 0;
                    color: #f56c6c;
                }
            }
            .field {
                margin-left: 10px;
            }
        }
        .end {
            justify-self: flex-end;
        }
    }
</style>
