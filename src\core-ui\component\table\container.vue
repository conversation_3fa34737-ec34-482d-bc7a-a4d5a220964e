<template>
    <div
        ref="container"
        class="container"
        :class="{
            'core-ui-table-container': extraStyle,
            'display-left-tree': displayLeftTree && !hiddenTree,
        }"
    >
        <div
            v-if="title || $slots['header-right'] || $slots['title']"
            class="header core-ui-custom-header"
        >
            <div class="title">
                <slot v-if="$slots['title']" name="title"></slot>
                <span v-else>{{ title }}</span>
            </div>
            <div class="flex-shrink-0">
                <slot name="header-right" />
                <el-button size="mini" v-if="refreshBtn" @click="search()">
                    <i class="el-icon-refresh icon-refresh"></i>
                    刷新
                </el-button>
            </div>
        </div>
        <table-filter
            ref="filter"
            v-if="showTableFilter"
            v-bind="$attrs"
            :tableFilter="tableConfig.filter"
            :outFilter="tableConfig.outFilter"
            :metaFilters="metaFilters"
            :showExpand="showExpand"
            :tagGroups="tagGroups"
            :defaultFilterIsExpand="defaultFilterIsExpand"
            :handlerFilter="handlerFilter"
            :customSearch="customSearch"
            @search="search"
        >
            <template slot="btn-right">
                <slot name="btn-right"></slot>
            </template>
        </table-filter>
        <div
            v-loading="loading"
            class="table"
            :class="[tableClass, tableConfig.oneTab ? 'one-tab' : '']"
        >
            <template v-if="currentPageName && pageDatas.length">
                <div class="d-flex d-flex-item-center table-tabs">
                    <el-tabs
                        v-model="currentPageName"
                        :type="cardTabs ? 'card' : ''"
                        @tab-click="handleClick"
                    >
                        <el-tab-pane
                            v-for="item in pageDatas"
                            :key="item.name"
                            :label="item.name + '(' + item.record_count + ')'"
                            :name="item.name"
                        >
                        </el-tab-pane>
                    </el-tabs>
                    <slot name="pages-tabs-right" />
                </div>
                <div
                    v-for="item in pageDatas"
                    v-show="currentPageName === item.name"
                    :key="item.name"
                >
                    <table-filter
                        v-if="
                            showFilter &&
                            !tableConfig.oneTabFilter &&
                            !hideFilter
                        "
                        ref="filters"
                        v-bind="$attrs"
                        :tableFilter="tableConfig.filter"
                        :outFilter="tableConfig.outFilter"
                        :metaFilters="metaFilters"
                        :showExpand="showExpand"
                        :tagGroups="tagGroups"
                        :currentPageName="currentPageName"
                        @search="search"
                    >
                        <template slot="btn-right">
                            <slot name="btn-right"></slot>
                        </template>
                    </table-filter>
                    <slot
                        name="table"
                        :data="item.rows"
                        :currentPageName="currentPageName"
                        :dataInit="dataInit"
                        :emptyText="emptyText"
                        :index="item.item_index"
                    />
                    <div
                        class="d-flex justify-content-center pagination"
                        v-if="
                            showPageIndex &&
                            (alwaysShowPageIndex ||
                                item.record_count > pageSizes[0])
                        "
                    >
                        <el-pagination
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            :current-page="item.item_index"
                            :page-sizes="pageSizes"
                            :page-size="pageSizes[0]"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="outCustomTotal || item.record_count"
                        >
                        </el-pagination>
                    </div>
                </div>
            </template>
            <template v-else>
                <slot
                    name="table"
                    :emptyText="emptyText"
                    :data="items"
                    :dataInit="dataInit"
                    :index="index"
                />
                <div
                    class="d-flex justify-content-center pagination"
                    v-if="
                        showPageIndex && (alwaysShowPageIndex || total > size)
                    "
                >
                    <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="index"
                        :page-sizes="pageSizes"
                        :page-size="size"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="total || 0"
                    >
                    </el-pagination>
                </div>
            </template>
        </div>

        <div class="left-tree" v-if="displayLeftTree && !hiddenTree">
            <tree-view
                v-if="listTree"
                ref="treeView"
                v-model="listTree"
                :treeTitle="treeTitle"
                :modelName="listTree.treeModelName"
                :rootNode="listTree.treeRootNodeValue"
                :reset-action-label="listTree.cancelButtonLabel"
                :types="listTree.searchProperties"
                :prefilters="treePrefilters"
                :auto="true"
                :treeConfig="treeConfig"
                :curNodeKey="curNodeKey"
                @node-select-change="handleTreeViewClick"
            ></tree-view>
        </div>
        <export-pop
            v-model="showExportPop"
            :percent="loadingMsg.percent"
            :status="loadingMsg.status"
            :useMockPercent="useMockPercent2Export"
            @cancel="cancelExport"
        ></export-pop>
        <export-apply-pop
            v-model="showExportApplyPop"
            :exportFormsValue="exportFormsValue"
        >
        </export-apply-pop>
    </div>
</template>

<script lang="ts">
    import { getAllFilters, ListEasy } from "uniplat-sdk"
    import {
        ListRow,
        ListTypes,
        metaFilter,
        prefilters,
        TagManagerTypes,
        TreeTypes,
    } from "uniplat-sdk/build/main/def"
    import { Component, Prop, Ref } from "vue-property-decorator"
    import TableFilter from "./filter.vue"
    import { BaseTable } from "./base-table"
    import {
        assign,
        cloneDeep,
        find,
        findIndex,
        flatMap,
        forEach,
        get,
        groupBy,
        isArray,
        map,
        some,
        sortBy,
        split,
    } from "lodash"
    import { FormType, tableFilterConfigGenerator } from "@/core-ui/component/form"
    import { UICore } from "@/core-ui/service/setup"
    import ExportPop from "./export-pop.vue"
    import TreeView from "../tree/tree-view.vue"
    import { Loading } from "element-ui"
    import { ExcelGenerator } from "./excel-generator"
    import Axios from "axios"
    import ExportApplyPop from "./export-apply-pop.vue"
    import { exportDataTypeMap } from "."

    @Component({ components: { TableFilter, ExportPop, TreeView, ExportApplyPop } })
    export default class TableContainer extends BaseTable<any> {
        @Ref() private readonly treeView!: any

        @Ref("container") readonly containerRef!: HTMLElement

        public metaFilters: metaFilter[] = []
        private tagGroups: TagManagerTypes.TagGroup[] = []
        private filterData: Record<string, any> | null = null

        private treeValue = ""
        private listTree:
            | ListTypes.getListDataRequestResult<ListRow>["meta"]["tree"]
            | null = null
        private treePrefilters = []

        private currentQueryParams: Partial<
            ListTypes.queryPropsEasy & {
                filterData: Record<string, any>
            }
        > = {}

        private showExportApplyPop = false
        private exportFormsValue: {
            model_name: string
            data_type: string
            parameters: string
        } | null = null

        @Prop()
        private title?: string

        @Prop({ default: "" }) curNodeKey!: any

        @Prop({ default: true })
        private extraStyle!: boolean

        @Prop({ default: "" })
        private tableClass!: string

        @Prop({ default: () => null })
        private treeConfig!: any

        /** container入参，用来控制tagFilter的exclude等参数 */
        @Prop({ default: null })
        private tagFilterConfig!: any

        @Prop({ default: true })
        private showPageIndex!: boolean

        @Prop({ default: true })
        private alwaysShowPageIndex!: boolean

        @Prop({ default: false })
        private showExpand!: boolean

        /** 针对领域服务filter */
        @Prop({ default: "" })
        private outFilterDataKey!: string

        @Prop({ default: false })
        private displayLeftTree!: boolean

        @Prop({ default: "" })
        private treeTitle!: string

        @Prop()
        private readonly model!: ListEasy

        @Prop({ default: false })
        private readonly refreshBtn!: boolean

        @Prop({ default: true })
        private defaultFilterIsExpand!: boolean

        @Prop({ default: false })
        private readonly cardTabs?: boolean

        @Prop({ default: false })
        private readonly useDefaultSort2Tabs?: boolean

        @Prop({ default: true })
        private readonly useMockPercent2Export?: boolean

        @Prop()
        private readonly customPageSize?: number[]

        @Prop({ default: true })
        private readonly openLoadingTextTip?: boolean

        /** 针对领域服务导出的数据处理 */
        @Prop({ default: () => (data: any[]) => flatMap(data) })
        private readonly handlerDomainServiceExport!: (data: any[]) => any[]
        /** 针对领域服务导出的数据处理 */

        @Prop({ default: () => null })
        private readonly handlerExport!: (
            template_name: string,
            path: string
        ) => any

        @Prop({ default: false })
        private readonly prefiltersAll?: boolean

        @Prop({ default: false })
        private readonly hideFilter?: boolean

        @Prop({ default: 0 })
        private readonly outCustomTotal?: number

        @Ref()
        filter?: any

        @Ref()
        filters?: TableFilter[] | any[]

        @Prop({ default: null })
        handlerFilter?: any

        /** 自定义点击查询按钮的操作 */
        @Prop({ default: false })
        private readonly customSearch?: boolean

        /** 领域服务传默认值无效，暂时增加此参数 */
        @Prop({ default: false })
        private readonly queryFilterChanged?: boolean

        private get emptyText() {
            return this.loading ? "加载中..." : "暂无数据"
        }

        private get showFilter() {
            return !!(
                (this.tableConfig.filter || []).filter((i) => !i.hide)?.length ||
                this.tableConfig.outFilter?.length
            )
        }

        private hiddenTree = true

        // 有tab 但是只用上面的filter
        private get isTabButOneFilter() {
            return this.tableConfig.oneTabFilter
        }

        private get showTableFilter() {
            return (
                this.showFilter &&
                (!this.pageDatas.length || this.isTabButOneFilter) &&
                !this.hideFilter
            )
        }

        async mounted() {
            if (this.customPageSize) {
                this.pageSizes = this.customPageSize
            }
            if (this.tableConfig.defaultPageSize) {
                this.size = this.tableConfig.defaultPageSize
                if (!this.pageSizes.includes(this.size)) {
                    this.pageSizes.push(this.size)
                    this.pageSizes = sortBy(this.pageSizes)
                }
            }
            if (!this.tableConfig.domainService && !this.tableConfig.model) {
                return new Error(
                    "tableConfig 中 domainService 、 model 必须设置一个"
                )
            }
            this.onFilterChanged()
            this.updateEmptyText(true)
        }

        private updateEmptyText(isLoading?: boolean): void {
            if (!this.openLoadingTextTip) {
                return
            }
            this.$nextTick(() => {
                const containerRef = this.containerRef

                if (containerRef) {
                    const emptyTextElements: NodeListOf<HTMLElement> =
                        containerRef.querySelectorAll(".el-table__empty-text")

                    emptyTextElements.forEach((element) => {
                        if (isLoading) {
                            element.textContent = "加载中"
                        } else {
                            element.textContent = "暂无数据"
                        }
                    })
                }
            })
        }
        private getFilterDataFromFilterCom(filterData?: Record<string, string>) {
            let treeObj = {}
            if (this.listTree) {
                treeObj = { [this.listTree.field]: this.treeValue }
            }
            if (this.isTabButOneFilter || !this.currentPageName) {
                const res = filterData || this.filter?.getFilterData() || null
                res && Object.assign(res, { ...treeObj })
                this.filterData = res
            } else {
                if (!this.filters) {
                    return
                }
                const index = findIndex(this.pageDatas, {
                    name: this.currentPageName,
                })
                const res =
                    filterData || this.filters[index]?.getFilterData() || null
                res && Object.assign(res, { ...treeObj })
                this.pageDatas[index].filterData = res
            }
        }

        private resetLeftTree() {
            this.treeView && this.treeView.reset()
            this.treeValue = ""
        }

        private handleTreeViewClick(nodeList: TreeTypes.Node[]) {
            this.treeValue = (nodeList[0]?.id as string) || ""
            this.$emit("treeClick", this.treeValue, nodeList[0])
            this.search()
        }

        private search(filterData?: Record<string, string>, reset?: boolean) {
            if (reset && this.listTree) {
                this.resetLeftTree()
            }
            if (reset) {
                this.$emit("reset")
            }
            if (this.customSearch && filterData) {
                return this.$emit("onSearch", filterData)
            }
            this.getFilterDataFromFilterCom(filterData)
            return this.reload(true)
        }

        private setFilter() {
            if (this.tableConfig.model) {
                this.tableConfig.model.clearFilter()
                this.tableConfig.model.clearPrefilter()
            }
            const filter: ListTypes.KeyValueFilters = []
            let filterData: Record<string, any> | null = null
            if (this.isTabButOneFilter || !this.currentPageName) {
                filterData = this.filterData
            } else {
                filterData =
                    find(this.pageDatas, {
                        name: this.currentPageName,
                    })?.filterData || null
            }
            filterData = { ...filterData }
            let tempPreFilter = cloneDeep(this.tableConfig.preFilter)
            if (this.tableConfig.handleFilterData && filterData) {
                filterData = this.tableConfig.handleFilterData(filterData)
                tempPreFilter = assign(tempPreFilter, filterData.preFilter)
            }
            // 处理filter 和 tag
            const tagFilters: TagManagerTypes.TagFilter[] = []
            _.forEach(filterData, (v, k) => {
                if (isArray(v)) {
                    if (!v.filter((i) => i).length) {
                        return
                    }
                }
                if (v || v === false) {
                    let f = find(this.tableConfig.filter, { prop: k })
                    if (!f) {
                        f = find(this.tableConfig.outFilter, { prop: k })
                    }
                    if (f?.type === FormType.Cascader) {
                        v = split(v, ",").filter((i) => i)
                    }
                    if (
                        f?.type === FormType.IntentSearch ||
                        f?.type === FormType.IntentSearchRemote
                    ) {
                        try {
                            v = JSON.parse(v || "[]")
                            if (!v.length) {
                                return
                            }
                        } catch {}
                    }
                    // Select 必须判断是否有逗号，因为前端Select后端不一定是Select
                    if (
                        ((f?.type === FormType.Select ||
                            f?.type === FormType.Select2) &&
                            v.toString().includes(",")) ||
                        f?.useTag
                    ) {
                        v = split(v, ",").filter((i) => i)
                    }
                    if (f?.useTag) {
                        if (!f.useTag.includes("*") && !f.useTag.includes(",")) {
                            tagFilters.push({
                                tagGroup: f.useTag,
                                tags: v,
                                matchMode: get(f, "option.matchMode") || "any",
                            })
                        } else {
                            let tags = map(v, (i) => {
                                const tag = split(i, ":::")
                                return {
                                    tagGroup: tag[0],
                                    tag: tag[1],
                                }
                            })
                            const isMultiple = some(v, (i) => i.includes(":::"))
                            if (isMultiple) {
                                tags = tags.filter((t) => t.tag)
                            }
                            const groups = map(
                                groupBy(tags, "tagGroup"),
                                (item) => {
                                    return {
                                        tagGroup: item[0].tagGroup,
                                        tags: map(item, "tag"),
                                        ...(this.tagFilterConfig
                                            ? this.tagFilterConfig()
                                            : {}),
                                    }
                                }
                            ) as TagManagerTypes.TagFilter[]
                            forEach(groups, (item) => {
                                tagFilters.push(item)
                            })
                        }
                    } else {
                        const d = {
                            property: k,
                            value: this.handlerFilterData(v, f),
                            ...f?.keyValueFilter,
                        }
                        filter.push(d)
                        this.tableConfig.model &&
                            this.tableConfig.model.addFilter(d)
                    }
                }
            })
            // 处理preFilter
            const prefilter: prefilters = []
            if (this.tableConfig.handlePreFilterData && tempPreFilter) {
                tempPreFilter = this.tableConfig.handlePreFilterData(
                    tempPreFilter,
                    this.currentPageName
                )
            }
            _.forEach(tempPreFilter, (value, property) => {
                prefilter.push({ property, value })
            })
            if (tempPreFilter && this.tableConfig.model) {
                if (this.prefiltersAll) {
                    this.tableConfig.model.addPrefilterAll(tempPreFilter as any)
                } else {
                    this.tableConfig.model.addPrefilter(tempPreFilter)
                }
            }
            this.currentQueryParams = {
                filters: filter,
                tagFilters: tagFilters,
                prefilters: this.prefiltersAll ? (tempPreFilter as any) : prefilter,
                filterData,
            }
            try {
                const fn = (this.$parent as any)?.updateQueryParams
                const listQuery = (this.tableConfig.model as any)._listQuery as any

                fn &&
                    fn({
                        ...this.currentQueryParams,
                        filters: Array.isArray(listQuery.listQueryParams.filters)
                            ? getAllFilters(
                                  listQuery.listQueryParams.filters,
                                  listQuery.listQueryParams.slave_filters
                              )
                            : listQuery.listQueryParams.filters,
                    })
            } catch (e) {
                this.$emit("updateQueryParams", this.currentQueryParams)
            }
            return filter
        }

        public onFilterChanged() {
            this.loading = true
            this.pageInit && this.setFilter()
            return this.getData()
                .catch((r) => {
                    console.log("rrrr", JSON.parse(JSON.stringify(r)))
                    this.items = []
                    this.total = 0
                    this.$emit("setTotal", 0)
                    this.$emit("getData", [])
                    throw Error(r)
                })
                .finally(() => {
                    if (!this.items.length && this.index > 1) {
                        this.index--
                        this.onFilterChanged()
                        return
                    }
                    this.loading = false
                })
        }

        public queryTab(next?: boolean) {
            const currentPage = this.getCurrentPage()
            if (next) currentPage.item_index += 1
            return this.tableConfig
                .model!.queryTab2(this.currentPageName, {
                    pageIndex: currentPage.item_index,
                    item_size: currentPage.item_size,
                    ...this.currentQueryParams,
                })
                .then((r) => {
                    currentPage.record_count = r.record_count
                    currentPage.rows = UICore.buildRows(
                        r.rows,
                        this.tableConfig.predict!
                    )
                    this.$emit("getRows", r.rows)
                    this.$emit("getCurrentPageRows", currentPage.rows)
                    this.$emit("getData", this.pageDatas, this.currentPageName)
                    this.$emit("currentRowCount", r.rows?.length || 0)
                    return r
                })
                .finally(() => {
                    this.updateEmptyText(false)
                })
        }

        public setFilterData() {}

        private handlerFilterData(v: any, f: any) {
            if (typeof v === "string" && f?.type === FormType.Text) {
                return v.replaceAll("%", "\\%").replaceAll("_", "\\_")
            }
            return v
        }

        pageInit = false
        dataInit = false

        private async getData() {
            if (!this.tableConfig.model) {
                if (this.queryFilterChanged) {
                    this.getFilterDataFromFilterCom()
                    this.setFilter()
                }
                // 直接用post 后台没区分
                const domainService = this.tableConfig.domainType || "post"
                const prefilter = this.tableConfig.preFilter || {}
                let payload: any = {
                    ...this.currentQueryParams.filterData,
                    ...prefilter,
                    page_index: this.index,
                    page_size: this.size,
                }
                // 针对领域服务 把筛选项外面再包一层
                if (this.outFilterDataKey) {
                    payload = {
                        ...prefilter,
                        [this.outFilterDataKey]: [
                            ...(this.currentQueryParams.filters || []),
                        ],
                        page_index: this.index,
                        page_size: this.size,
                    }
                }
                return this.tableConfig
                    .domainService!.request<
                        unknown,
                        unknown,
                        {
                            total_count: string | number
                            total: string | number
                            page_size: number
                            page_index: number
                            tagGroups?: TagManagerTypes.TagGroup[]
                            data: Array<unknown>
                            reports: Array<unknown>
                            count?: number
                        }
                    >(domainService, {
                        [domainService === "post" ? "data" : "params"]: payload,
                    })
                    .then((r) => {
                        this.$emit("loaded", r.data, r)
                        this.dataInit = true
                        this.tagGroups = r?.tagGroups || []
                        this.total = +r.total_count || +r.total || +(r.count || 0)
                        this.$emit("setTotal", this.total)
                        this.items = r.data || r.reports || []
                        this.$emit("getData", this.items)
                    })
                    .finally(() => {
                        this.updateEmptyText(false)
                    })
            } else if (this.currentPageName) {
                return this.queryTab()
            } else {
                if (!this.pageInit) {
                    if (this.prefiltersAll) {
                        this.tableConfig.model.addPrefilterAll(
                            (this.tableConfig.preFilter || []) as any
                        )
                    } else {
                        this.tableConfig.model.addPrefilter(
                            this.tableConfig.preFilter || {}
                        )
                    }

                    return this.tableConfig.model
                        .query({
                            pageIndex: this.index,
                            item_size: 1,
                            ...this.currentQueryParams,
                        })
                        .then(async (r) => {
                            this.pageInit = true
                            this.buildTabPageData(r.pageData.meta.pages)
                            this.tagGroups = r.pageData.tagGroups
                            this.metaFilters = r.pageData.meta.filters
                            if (this.displayLeftTree) {
                                this.listTree = r.pageData.meta.tree
                                this.hiddenTree = !this.listTree
                                if (this.listTree && this.listTree.prefilters) {
                                    this.treePrefilters = this.listTree
                                        .prefilters as []
                                }
                            }
                            if (this.tableConfig.useRemoteFilter) {
                                const remoteFilter = tableFilterConfigGenerator(r)
                                this.tableConfig.filter = [
                                    ...remoteFilter,
                                    ...(this.tableConfig.filter || []),
                                ]
                            }
                            this.$emit("getMetaInfo", r.pageData.meta)
                            this.$emit("setMetaFilter", this.metaFilters)
                            await this.$nextTick()
                            await this.search()
                            await this.getTabPageDefaultTotal()
                        })
                        .finally(() => {
                            this.updateEmptyText(false)
                        })
                }
                return this.tableConfig.model
                    .search({
                        ...this.currentQueryParams,
                        pageIndex: this.index,
                        item_size: this.size,
                    })
                    .then((r) => {
                        this.$emit("setTotal", r.record_count)
                        this.$emit("loaded", r)
                        this.dataInit = true
                        this.items = UICore.buildRows(
                            r.rows,
                            this.tableConfig.predict!
                        )
                        this.total = r.record_count
                        this.$emit("getRows", r.rows)
                        this.$emit("getData", this.items)
                    })
                    .finally(() => {
                        this.updateEmptyText(false)
                    })
            }
        }

        public setMetaFilters(metaFilters: metaFilter[]) {
            this.metaFilters = { ...this.metaFilters, ...metaFilters }
        }

        private buildTabPageData(pages: { name: string }[]) {
            let pagesDatas = map(pages, (i) => {
                return {
                    name: i.name,
                    item_size: this.size,
                    record_count: 0,
                    item_index: 1,
                    rows: [],
                    filterData: null,
                }
            }).sort((a, b) => {
                if (this.useDefaultSort2Tabs) {
                    return 1
                }
                return a.name.length - b.name.length
            })
            if (this.tableConfig.tabPages?.length) {
                pagesDatas = pagesDatas
                    .filter((i) => this.tableConfig.tabPages?.includes(i.name))
                    .sort((a, b) => {
                        if (this.useDefaultSort2Tabs) {
                            return 1
                        }
                        return (
                            this.tableConfig.tabPages!.indexOf(a.name) -
                            this.tableConfig.tabPages!.indexOf(b.name)
                        )
                    })
            }
            if (pagesDatas.length) {
                this.pageDatas = pagesDatas
                if (this.tableConfig.defaultPage) {
                    this.currentPageName = this.tableConfig.defaultPage
                } else {
                    this.currentPageName = pagesDatas[0].name
                }
            }
        }

        private getTabPageDefaultTotal() {
            if (this.pageDatas.length && this.tableConfig.model) {
                this.tableConfig.model
                    .getPageCountV2({
                        pageIndex: 1,
                        item_size: 0,
                        ...this.currentQueryParams,
                    })
                    .then((r) => {
                        _.forEach(r.page_datas, (item) => {
                            const page = find(this.pageDatas, { name: item.name })
                            page && (page.record_count = item.record_count)
                        })
                        this.$emit("getTabPageDefaultTotal", r)
                    })
            }
        }

        private handleClick() {
            this.$emit("tabName", this.currentPageName)
            this.$nextTick(() => {
                const currentRowCount = find(this.pageDatas, {
                    name: this.currentPageName,
                })?.rows.length
                if (!currentRowCount) {
                    this.updateEmptyText(true)
                    this.getFilterDataFromFilterCom()
                    this.setFilter()
                    this.onFilterChanged()
                } else {
                    this.$emit("currentRowCount", currentRowCount)
                }
            })
        }

        public reload(resetPage = true) {
            this.updateEmptyText(true)
            resetPage && (this.index = 1)
            forEach(this.pageDatas, (i) => {
                resetPage && (i.item_index = 1)
                i.rows = []
            })
            this.setFilter()
            this.getTabPageDefaultTotal()
            return this.onFilterChanged()
        }

        private setTotal() {
            this.$emit("getTotal", this.total)
        }

        async exportExcelUniplatV2(e: {
            template_name: string
            file_name?: string
            data_type?: string
        }) {
            if (+(localStorage.getItem("showExportTask") || "")) {
                return this.exportExcelUniplatV2Apply(e)
            }
            if (!this.tableConfig.model) {
                return Promise.reject("no model")
            }
            await this.onFilterChanged()
            const listQueryParams = (this.tableConfig.model as any)._listQuery
                .listQueryParams
            ;(this.tableConfig.model as any).listQueryParams = listQueryParams
            const model = this.tableConfig.model
            this.loadingMsg = {
                percent: 0,
                status: "",
                process: null,
            }
            this.showExportPop = true
            return model
                .postParamsForExcel({
                    page_name: this.currentPageName,
                    template_name: e.template_name,
                })
                .then((eid) => {
                    const process = model.exportToExcelV2(eid)((p, status) => {
                        this.loadingMsg = {
                            percent: p,
                            status,
                            process,
                        }
                    })
                    return process.awaiting.then((r) => {
                        if (this.handlerExport) {
                            const flag = this.handlerExport(e.template_name, r)
                            if (flag) return
                        }
                        if (e.file_name) {
                            Axios.get(r, {
                                responseType: "blob",
                            }).then((response) => {
                                const blob = new Blob([response.data], {
                                    type: response.headers["content-type"],
                                })
                                const link = document.createElement("a")
                                link.href = window.URL.createObjectURL(blob)
                                link.download = e.file_name || ""

                                link.click()
                                window.URL.revokeObjectURL(link.href)
                            })
                        } else {
                            window.open(r)
                        }
                    })
                })
                .catch((e) => {
                    this.$message.error(e)
                })
                .finally(() => {
                    this.showExportPop = false
                })
        }

        async exportExcelUniplatV2Apply(e: {
            template_name: string
            file_name?: string
            data_type?: string
        }) {
            if (!this.tableConfig.model) {
                return Promise.reject("no model")
            }
            await this.onFilterChanged()
            if (this.total > 20000) {
                return this.$message.error(
                    `导出数据过多(${this.total}条)，请缩小范围后再导出`
                )
            }
            const listQuery = (this.tableConfig.model as any)._listQuery as any
            const parametersObj = {
                name: listQuery.list_name,
                filters: Array.isArray(listQuery.listQueryParams.filters)
                    ? getAllFilters(
                          listQuery.listQueryParams.filters,
                          listQuery.listQueryParams.slave_filters
                      )
                    : listQuery.listQueryParams.filters,
                prefilters: listQuery.listQueryParams.prefilters,
                order_obj: listQuery.listQueryParams.order_obj,
                workflowType: listQuery.listQueryParams.workflowType,
                tabName: listQuery.listQueryParams.tabName,
                tagFilters: listQuery.listQueryParams.tagFilters,
                page_name: this.currentPageName,
                template_name: e.template_name,
            }
            const visitedViews = JSON.parse(
                sessionStorage.getItem("visitedViews") as string
            )
            const currentView = visitedViews.find(
                (i: any) => i.name === this.$route.name
            ) as any
            let dataType = ""
            if (e?.data_type) {
                dataType = e.data_type
            } else {
                dataType =
                    currentView?.breadcrumb
                        ?.map((item: any) => item.label)
                        .join("-") ||
                    currentView?.title ||
                    ""
                if (this.currentPageName) {
                    dataType += `-${this.currentPageName}`
                }
            }
            const mapGet = `${listQuery.api.model_name}-${listQuery.list_name}`
            if (exportDataTypeMap.get(mapGet)) {
                dataType = `${exportDataTypeMap.get(mapGet)}`
                if (this.currentPageName) {
                    dataType += `-${this.currentPageName}`
                }
            }

            this.exportFormsValue = {
                model_name: listQuery.api.model_name,
                data_type: dataType,
                parameters: JSON.stringify({
                    parameters: JSON.stringify(parametersObj),
                }),
            }

            this.showExportApplyPop = true
        }

        private cancelExport() {
            const fn = this.loadingMsg.process?.close
            fn && fn()
        }

        /** 导出领域服务的excel */
        private async domainExportToExcel() {
            const page_size = 100
            const totalPages = Math.ceil(this.total / page_size)
            const query = (page_index = 1) => {
                return this.tableConfig!.domainService?.post({
                    ...this.filterData,
                    page_index,
                    page_size,
                })
            }
            const loading = Loading.service({})
            const promises = new Array(totalPages).fill(0).map((_, index) => {
                return new Promise((resolve) => {
                    setTimeout(() => {
                        resolve(query(index + 1))
                    }, index * 200)
                })
            })
            const results = (await Promise.all(promises)) as any
            loading.close()
            const rows = this.handlerDomainServiceExport(results)
            const columns = this.tableConfig.column || results[0]?.column || []
            ExcelGenerator.execute({
                primaryRows: [],
                columns: map(columns, (e) => e.label),
                rows,
                fileName: "导出",
            })
        }

        public async exportToExcel() {
            this.exportExcelUniplatV2({ template_name: "默认导出" })
            return
            // if (!this.tableConfig.model) {
            //     if (!this.tableConfig.domainService) {
            //         return Promise.reject("no model")
            //     } else {
            //         this.domainExportToExcel()
            //         return
            //     }
            // }
            // await this.onFilterChanged()
            // const listQueryParams = (this.tableConfig.model as any)._listQuery
            //     .listQueryParams
            // ;(this.tableConfig.model as any).listQueryParams = listQueryParams
            // this.loadingMsg = {
            //     percent: 0,
            //     status: "",
            //     process: null,
            // }
            // const process = this.tableConfig.model.exportToExcel({
            //     page_name: this.currentPageName,
            // })((p, status) => {
            //     this.loadingMsg = {
            //         percent: p,
            //         status,
            //         process,
            //     }
            // })
            // this.showExportPop = true
            // return process.awaiting
            //     .then((r) => {
            //         window.open(r, "_blank")
            //     })
            //     .catch((e) => {
            //         this.$message.error(e)
            //     })
            //     .finally(() => {
            //         this.showExportPop = false
            //     })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .container {
        &.display-left-tree {
            position: relative;
            padding-left: 250px;

            .header {
                margin-left: -250px;
            }

            .left-tree {
                width: 226px;
                height: calc(100% - 132px);
                background-color: #fff;
                position: absolute;
                top: 70px;
                left: 0px;
                min-height: 75vh;
            }
        }
    }
    .pb20 {
        padding-bottom: 20px;
    }
    .header {
        padding: 20px 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .icon-refresh {
            font-size: 14px;
            padding-right: 10px;
        }
        .title {
            font-size: 20px;
            font-weight: 500;
            line-height: 30px;
            width: 100%;
            color: @text-primary-header;
        }
    }
    .table {
        width: 100%;
        padding: 20px;
        background: #fff;
        ::v-deep .el-table {
            width: 100%;
            color: #333;
            th.el-table__cell {
                background-color: #fafafa;
                color: #888;
                font-weight: 400;
            }
            .el-table__fixed::before {
                height: 0;
            }
        }
        &.one-tab {
            ::v-deep.el-tabs__header {
                display: none;
            }
        }
    }
    .el-tabs {
        ::v-deep .el-tabs__item {
            line-height: 1;
            height: 32px;
        }
        ::v-deep .el-tabs__content {
            overflow: visible;
        }
        ::v-deep .el-tabs__header {
            margin: 0;
            .el-tabs__nav-wrap::after {
                background: transparent;
            }
        }
    }
    .pagination {
        margin-top: 30px;
    }
</style>
