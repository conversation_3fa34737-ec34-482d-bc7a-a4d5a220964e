<template>
    <div class="detail-container cus-table-tab-ui">
        <div class="d-flex justify-content-end opt-btn">
            <!-- <el-button v-if="showActiveName" @click="exportCurrent"
                >导出未通过数据</el-button
            > -->
            <!-- <el-button type="primary" @click="onBatchCheck">批量审核</el-button> -->
            <el-button
                v-if="['待审核', '审核未通过'].includes(activeName)"
                v-role="
                    'domain.data_products.service.collect_order_detail_api.batch_audit'
                "
                type="primary"
                @click="onAutoAudit"
                >自动审核</el-button
            >
        </div>
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            :customPageSize="[10, 20, 50]"
            @tabName="onTabNameChange"
            @getData="getCurrentPageRows"
            class="container-index container shadow"
        >
            <div
                slot="table"
                class="u-p-20 bg-white"
                :class="{
                    'custom-selector-table': ['待审核', '审核未通过'].includes(
                        activeName
                    ),
                }"
            >
                <div class="select-each-num">
                    <el-checkbox
                        @click.stop.prevent
                        @focus.stop.prevent
                        class="table-select-chechbox"
                        :disabled="!tableSelectedVisible"
                        v-model="tableSelected"
                        :indeterminate="indeterminate"
                        @change="selectedChanged"
                    />
                    <el-select
                        ref="typeSelect"
                        v-model="selectType"
                        class="header-select"
                        popper-class="header-select-popper"
                        @change="selectTypeChange"
                        :title="selectTopTitle"
                    >
                        <el-option label="本页" :value="0" />
                        <el-option label="全部" :value="1" />
                        <el-option :label="topItemText" :value="2">
                            前
                            <input
                                v-model="top"
                                class="top-input"
                                @click.stop
                                @focus.stop
                                @input="onInputChange"
                            />
                            条
                        </el-option>
                    </el-select>
                </div>
                <common-table
                    ref="commonTable"
                    :data="curData"
                    :columns="columns"
                    @handleSelectionChange="handleSelectionChange"
                >
                    <template slot="selectV2" slot-scope="scope">
                        <el-checkbox
                            @change="change($event, scope.row)"
                            v-model="scope.row.selected"
                            :disabled="scope.row.selectedDisabled"
                        ></el-checkbox>
                    </template>
                    <div slot="h" slot-scope="scope">
                        <el-button
                            v-if="
                                ['全部', '待审核'].includes(
                                    scope.row['审核状态']
                                )
                            "
                            type="text"
                            @click="goAudit(scope.row.id)"
                        >
                            审核
                        </el-button>
                        <el-button
                            type="text"
                            @click="goDetail(scope.row.accessKey)"
                        >
                            详情
                        </el-button>
                    </div>
                </common-table>
            </div>
        </table-container>

        <DialogAudit
            v-model="displayDialogAudit"
            :curId="curId"
            :selectedIds="checkIds"
            @refresh="refresh"
            :topNum="top"
            :selectType="selectType"
            :activeName="activeName"
        >
        </DialogAudit>
    </div>
</template>

<script lang="ts">
    import { routesMap } from "@/router/direction"
    import { Component, Prop, Ref } from "vue-property-decorator"
    import { tableConfig, columns, columns2 } from "../record-list"
    import { predict } from "@/views/pages/collect-task-manage/task-info-manage/collect-info-manage/index"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table3/container.vue"
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { DetailController, getBtnViewsByMetaActions } from "../base"
    import { action } from "uniplat-sdk"
    import DialogAudit from "../components/dialog-audit.vue"
    import { without } from "lodash"
    import { DetailRow } from "../model"
    import { sdk } from "@/service"

    enum SelectType {
        Page = 0,
        All = 1,
        Top = 2,
    }

    interface AutoAuditParams {
        select_all?: boolean
        id_list?: string[]
        top?: number
        collect_root_task_id: string
        filters?: Record<string, any>
    }

    @Component({
        components: {
            TableContainer,
            CommonTable,
            DialogAudit,
        },
    })
    export default class RecordList extends BaseTableController<any> {
        @Prop({ default: () => {} })
        private detail!: DetailRow

        @Ref()
        private commonTable!: CommonTable

        tableConfig: any | null = null

        private columns = columns()

        private selectType = SelectType.Page

        private displayDialogAudit = false

        private tableSelectedVisible = true

        private tableSelected = false

        private curData: any = []

        private middleStatus = false
        private indeterminate = false

        private curId = ""

        private detailId = ""

        private viewActions: action[] = []

        private checkIds: string[] = []

        private detailName = ""

        private showActiveName = false

        private activeName = ""

        private top = 100

        // 缓存
        private configCache: {
            [key: string]: any
        } = {}

        refreshConfig = {
            fun: this.refresh,
            name: "task-info-record-list",
        }

        private get rootTaskId() {
            return this.detail.root_task_id || ""
        }

        private refresh() {
            this.reloadList(false)
        }

        private handlerData(data: any[]) {
            return data.map((e) => {
                return {
                    ...e,
                    selected: this.checkIds.includes(e.id),
                    selectedDisabled: false,
                }
            })
        }

        private change(selected: any, row: any) {
            if (selected && !this.checkIds.includes(row.id)) {
                this.checkIds.push(row.id)
            } else if (!selected) {
                this.checkIds = without(this.checkIds, row.id)
            }
            this.setAllSelectedStatus()
        }

        private goAudit(id: string) {
            this.curId = id
            this.displayDialogAudit = true
        }

        private onTabNameChange(activeName: string) {
            this.activeName = activeName
            // this.showActiveName = activeName === "审核未通过"

            this.checkIds = []
            this.resetTopSelectedStatus()
            this.getCurrentData(activeName)

            // 只在切换到"审核中"时更新配置
            if (activeName === "审核中") {
                this.updateTableConfigForAuditing(activeName)
            } else {
                if (
                    this.tableConfig.model.api.model_name ===
                    "collect_task_order_detail"
                ) {
                    this.updateTableConfigForAuditing(activeName)
                }
            }

            this.$nextTick(() => {
                if (["待审核", "审核未通过"].includes(activeName)) {
                    this.columns = columns2()
                } else {
                    this.columns = columns()
                }
            })
        }

        // 专门为"审核中"标签页更新配置
        private updateTableConfigForAuditing(activeName: string) {
            try {
                const pageListName =
                    activeName === "审核中"
                        ? "collect_task_order_detail"
                        : "collect_task_order_detail_doris"

                // 检查缓存中是否已有配置
                const cacheKey = `${pageListName}_${activeName}`
                if (this.configCache[cacheKey]) {
                    console.log(`使用缓存的配置: ${cacheKey}`)
                    this.detailName = this.configCache[cacheKey].detailName
                    this.tableConfig = this.configCache[cacheKey].tableConfig
                    return
                }

                const c =
                    DetailController.getPagePreFilterMetaInDetail(pageListName)

                if (!c) {
                    console.warn(`未找到页面列表 ${pageListName} 的配置`)
                    return
                }

                this.detailName = c.listName

                // 先清空当前配置
                this.tableConfig = null

                // 创建新的配置
                this.$nextTick(() => {
                    const newTableConfig = {
                        ...tableConfig(pageListName),
                        tabPages: [
                            "全部",
                            "待审核",
                            "审核中",
                            "审核通过",
                            "审核未通过",
                        ],
                        defaultPage: activeName,
                    }

                    // 缓存配置
                    this.configCache[cacheKey] = {
                        detailName: this.detailName,
                        tableConfig: newTableConfig,
                    }

                    this.tableConfig = newTableConfig
                    console.log(`已缓存配置: ${cacheKey}`)
                })
            } catch (error) {
                console.error("更新审核中配置失败:", error)
            }
        }

        private resetTopSelectedStatus() {
            this.tableSelectedVisible = true
            this.tableSelected = false
            this.indeterminate = false
            this.selectType = SelectType.Page
        }

        private exportCurrent() {
            this.exportToExcel()
        }

        private onBatchCheck() {
            if (this.selectType === SelectType.Page) {
                this.curId = ""
                if (!this.checkIds.length) {
                    return this.$message.warning("请选择数据")
                }

                this.displayDialogAudit = true
            } else {
                this.curId = ""
                this.displayDialogAudit = true
            }
        }

        private get selectTopTitle() {
            return this.selectType === SelectType.Top ? `前${this.top}条` : ""
        }

        private get topItemText() {
            if (this.top > 999) {
                return "前N条"
            }
            return `前${this.top}条`
        }

        private pageDatas: any[] = []

        private getCurrentPageRows(pageDatas: any[], name: string) {
            console.log("pageDatas")
            console.log(pageDatas)
            this.pageDatas = pageDatas
            this.getCurrentData(name)
        }

        private getCurrentData(name: string) {
            const t = this.pageDatas.find((e: any) => e.name === name)
            const rows = t?.rows || []

            const item_index = t?.item_index || 1
            const item_size = t?.item_size || 10
            const preLength = (item_index - 1) * item_size
            const baseIndexLine =
                this.top - preLength > 0 ? this.top - preLength : -999

            this.curData = rows.map((e: any, index: number) => {
                return {
                    ...e,
                    selected:
                        this.selectType === SelectType.Top
                            ? baseIndexLine > index
                            : false,
                    selectedDisabled:
                        this.selectType === SelectType.Top ||
                        this.selectType === SelectType.All,
                }
            })
            this.checkIds = []
            this.setAllSelectedStatus()
        }

        private onInputChange() {
            let num: any = this.top * 1

            if (isNaN(num)) {
                num = (this.top + "").replace(/[^\d]/g, "") || 0
            }

            if (num * 1 > 999) {
                this.top = 999
                this.selectTypeChange()
                return
            }
            this.top = num
            this.selectTypeChange()

            // console.log("num")
            // console.log(num)
            // console.log(this.top)
        }

        private selectTypeChange() {
            this.tableSelected = false
            if (this.selectType === SelectType.All) {
                this.tableSelectedVisible = false
                this.curData.forEach((item: any) => {
                    item.selected = false
                    item.selectedDisabled = true
                })
                return
            }
            if (this.selectType === SelectType.Top) {
                this.tableSelectedVisible = false
                this.curData.forEach((item: any, index: number) => {
                    if (index < this.top) {
                        item.selected = true
                    } else {
                        item.selected = false
                    }
                    item.selectedDisabled = true
                })
                return
            }
            this.curData.forEach((item: any) => {
                item.selected = false
                item.selectedDisabled = false
            })
            this.tableSelectedVisible = true
        }

        private selectedChanged(checked: boolean) {
            this.curData.forEach((item: any) => (item.selected = checked))
            this.checkIds = this.curData
                .filter((e: any) => e.selected)
                .map((e: any) => e.id)

            this.setAllSelectedStatus()
        }

        private setAllSelectedStatus() {
            const cLength = this.checkIds.length
            if (!cLength) {
                this.tableSelected = false
                this.indeterminate = false
            } else if (cLength === this.curData.length) {
                this.tableSelected = true
                this.indeterminate = false
            } else {
                this.tableSelected = false
                this.indeterminate = true
            }
        }

        private goDetail(id: number) {
            this.$router.push({
                name: routesMap.collectTaskManage.taskInfoManage.taskInfo
                    .recordListDetail,
                query: {
                    id: id + "",
                    type: "pre",
                    detailName: this.detailName,
                    from: this.$route.name,
                },
            })
            return id
        }

        private handleSelectionChange(d: { ids: string[]; rows: any[] }) {
            this.checkIds = d.rows.map((e) => e.id)
        }

        public noticeRegionIdUpdate(init: boolean) {
            this.detailId = this.$route.query.id as string
            if (init) {
                return this.init()
            }
            this.tableConfig = null
            this.$nextTick(() => {
                this.init()
            })
        }

        private init() {
            DetailController.getDetail(this.detailId).then(() => {
                const pageListName = "collect_task_order_detail_doris"
                const c =
                    DetailController.getPagePreFilterMetaInDetail(pageListName)
                this.detailName = c.listName
                this.tableConfig = tableConfig(pageListName)
            })
        }

        private getViewActionDisplayInfo(key: string) {
            return !!getBtnViewsByMetaActions(this.viewActions, key)
        }

        private onAutoAudit() {
            // 获取搜索参数
            const searchValues = this.getCurrentSearchValues()
            const regionCodes = (searchValues.region_province_code || "").split(",")
            const [
                region_province_code,
                region_city_code,
                region_district_code,
                region_town_code,
                region_village_code,
            ] = regionCodes

            const residenceCodes = (
                searchValues.residence_province_code || ""
            ).split(",")
            const [
                residence_province_code,
                residence_city_code,
                residence_district_code,
            ] = residenceCodes

            const householdCodes = (
                searchValues.household_province_code || ""
            ).split(",")
            const [
                household_province_code,
                household_city_code,
                household_district_code,
                household_town_code,
                household_village_code,
            ] = householdCodes

            const {
                collect_time,
                collector_id,
                collector_change_status,
                name,
                id_card,
                mobile,
            } = searchValues

            const params: {
                select_all?: boolean
                id_list?: string[]
                top?: number
                collect_root_task_id: string
                filters?: Record<string, any>
            } = {
                collect_root_task_id: this.rootTaskId,
            }
            if (this.selectType === SelectType.All) {
                params.select_all = true
            }
            if (this.selectType === SelectType.Top) {
                params.top = this.top
            }
            if (this.selectType === SelectType.Page) {
                params.id_list = this.checkIds
            }
            params.filters = {
                name: name,
                id_card: id_card,
                mobile: mobile,
                mgt_region_all: {
                    region_province_code: region_province_code,
                    region_city_code: region_city_code,
                    region_district_code: region_district_code,
                    region_town_code: region_town_code,
                    region_village_code: region_village_code,
                },
                household_region_all: {
                    household_province_code: household_province_code,
                    household_city_code: household_city_code,
                    household_district_code: household_district_code,
                    household_town_code: household_town_code,
                    household_village_code: household_village_code,
                },
                residence_region_all: {
                    residence_province_code: residence_province_code,
                    residence_city_code: residence_city_code,
                    residence_district_code: residence_district_code,
                },
                collect_time: {
                    start: collect_time === "" ? "" : collect_time[0],
                    end: collect_time === "" ? "" : collect_time[1],
                },
                collector_id_list:
                    collector_id === "" ? [] : JSON.parse(collector_id),
                collector_change_status_list:
                    collector_change_status === ""
                        ? []
                        : collector_change_status.split(","),
            }

            this.onAutoCheck(params)
        }

        // 获取当前搜索值
        private getCurrentSearchValues() {
            const tableRef = this.$refs.table as any

            if (tableRef && tableRef.$refs && tableRef.$refs.filters) {
                if (Array.isArray(tableRef.$refs.filters)) {
                    const currentPageIndex = this.pageDatas.findIndex(
                        (page: any) => page.name === this.activeName
                    )

                    if (
                        currentPageIndex >= 0 &&
                        tableRef.$refs.filters[currentPageIndex]
                    ) {
                        const filterData =
                            tableRef.$refs.filters[currentPageIndex].getFilterData()
                        return filterData
                    }
                } else {
                    const filterData =
                        tableRef.$refs.filters[0]?.getFilterData() || {}
                    return filterData
                }
            }
            return {}
        }

        // 自动审核
        private onAutoCheck(params: AutoAuditParams) {
            if (this.selectType === SelectType.Page && !params.id_list?.length) {
                this.$message.warning("请选择数据")
                return
            }
            if (this.selectType === SelectType.Top && !params.top) {
                this.$message.warning("请输入前N条")
                return
            }
            this.$confirm(
                "选择自动审核后，会将已选择信息添加到审核中的队列中，等待系统自动审核，期间可随时添加数据，也可进行其他操作，审核完成的数据可以在【审核通过】和【审核未通过】菜单中查看",
                "自动审核",
                {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                }
            ).then(() => {
                this.executeAuditAPI(params)
            })
        }

        // 执行审核接口
        private executeAuditAPI(params: AutoAuditParams) {
            console.log("params", params)

            sdk.core
                .domainService(
                    "data_products",
                    "collect_order_detail_api",
                    "batch_audit"
                )
                .post(params)
                .then((res) => {
                    this.$message.success("添加自动审核成功")
                    this.checkIds = []
                    // 清空所有行的选中状态
                    this.curData.forEach((item: any) => {
                        item.selected = false
                    })
                    this.setAllSelectedStatus()
                })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    @import "~@/css/table-container.less";

    .opt-btn {
        top: 20px;
        right: 20px;
        position: absolute;
        z-index: 999;
    }

    .detail-container {
        gap: 20px;
        min-height: 610px;
        position: relative;
        padding-top: 15px;

        .tree-box {
            background-color: #fff;
        }

        /deep/ .el-button {
            min-width: auto !important;
        }
    }

    .ml-auto {
        margin-left: auto;
    }

    .cus-table-tab-ui {
        margin-left: 20px;

        /deep/ .el-tabs__item {
            background-color: transparent !important;
            position: relative;
            min-width: 0px !important;
            padding-left: 0px !important;
            padding-right: 0px !important;
            margin-right: 40px;

            &.is-active {
                border-bottom: 3px solid #5782ec;
                color: #5782ec !important;

                &::after {
                    content: "";
                    position: absolute;
                    bottom: 0px;
                    left: 0px;
                    width: 100%;
                }
            }
        }
    }

    .select-each-num {
        position: absolute;
        display: none;
        left: 20px;
        width: 120px;
        top: 20px;
        cursor: pointer;
        z-index: 99;
        height: 64px;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 5px;
    }

    .custom-selector-table {
        position: relative;

        .select-each-num {
            display: flex !important;
        }

        /deep/ table {
            thead {
                & > tr {
                    &:first-child {
                        & > th {
                            padding: 20px 0;

                            &:first-child {
                                .cell {
                                    position: relative;
                                    top: -10px;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    .header-select {
        /deep/ .el-input__suffix {
            display: none;
        }
        /deep/ .el-input__inner {
            padding: 0;
            height: 16px;
            line-height: 16px;
            border: none;
            background-color: transparent;
            width: 58px;
            text-align: center;
            font-size: 13px;
            color: #5780ab;
            border-bottom: 1px solid #ccc;
            border-radius: 0;
        }
        &.no-checkbox /deep/ .el-input__inner {
            width: 50px;
            text-align: center;
        }
    }

    /deep/ .el-table__row {
        .el-checkbox__input.is-checked .el-checkbox__inner {
            background-color: var(--primary, #0bcc27) !important;
            border-color: var(--primary, #0bcc27) !important;

            &::after {
                border-color: #fff !important;
            }
        }
    }

    .top-input {
        width: 50px;
        height: 22px;
        line-height: 20px;
        padding: 0 2px;
        text-align: center;
        border-top: none;
        border-left: none;
        border-right: none;
        border-radius: 0;
        background-color: transparent;
        border-bottom: #5780ab 1px solid;
        color: #5780ab;

        &:focus {
            outline: none;
        }
    }
</style>
