<template>
    <div class="core-ui-table-container">
        <div class="core-ui-custom-header" v-if="row">
            <div class="title u-row-between u-flex">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
                <div class="u-flex">
                    <!-- <el-button
                    role="model.xg_position_collected.action.update"
                    type="primary"
                    plain
                    @click="toEdit"
                >
                    修改岗位信息
                </el-button> -->
                    <el-button
                        v-if="!row.status"
                        role="model.xg_position_collected.action.audit"
                        type="primary"
                        plain
                        @click="showPop = true"
                    >
                        处理
                    </el-button>
                    <el-button
                        v-if="row.status === 1"
                        role="model.xg_position_collected.action.send_sms"
                        type="primary"
                        plain
                        @click="sendAgentSMS"
                    >
                        发送邀请发布岗位短信
                    </el-button>
                    <el-button
                        v-if="row.status === 1 && !row.tag_agent"
                        role="model.invite_company_record.action.send_sms"
                        type="primary"
                        plain
                        @click="sendAgentSMS"
                    >
                        发送邀请入驻短信
                    </el-button>
                </div>
            </div>
        </div>
        <div class="u-p-20 bg-white">
            <div class="title u-m-b-20">岗位信息</div>
            <detail-row-col
                :labelStyle="labelStyle"
                :list="items"
                class="u-p-x-20"
            >
            </detail-row-col>
            <template v-if="showCompanyInfo">
                <div class="title u-m-y-20">企业信息</div>
                <detail-row-col
                    :labelStyle="labelStyle"
                    :list="items4"
                    class="u-p-x-20"
                >
                </detail-row-col>
            </template>
            <template v-if="showOddInfo">
                <div class="title u-m-y-20">驿站信息</div>
                <detail-row-col
                    :labelStyle="labelStyle"
                    :list="items5"
                    class="u-p-x-20"
                >
                </detail-row-col>
            </template>
            <div class="title u-m-y-20">网格信息</div>
            <detail-row-col
                :labelStyle="labelStyle"
                :list="items2"
                class="u-p-x-20"
            >
            </detail-row-col>
            <div class="title u-m-t-20">处理信息</div>
            <detail-row-col
                :labelStyle="labelStyle"
                :list="items3"
                class="u-p-x-20"
            >
            </detail-row-col>
            <detail-list :obj_id="row.id" v-if="row"></detail-list>
        </div>

        <common-pop
            v-model="showPop"
            v-if="handlePopConfig"
            v-bind="handlePopConfig"
            @refresh="init()"
        ></common-pop>
    </div>
</template>

<script lang='ts'>
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import {
        renDesensitizationView,
        renDesensitizationView2Remote,
    } from "@/views/components/common-comps"
    import { CommonPopConfig } from "@/views/components/common-pop"
    import CommonPop from "@/views/components/common-pop/index.vue"
    import { ColItem } from "@/views/components/detail-row-col"
    import DetailRowCol from "@/views/components/detail-row-col/index.vue"
    import { pageLoading } from "@/views/controller"
    import { Component, Vue } from "vue-property-decorator"
    import { handlePopConfig, PositionType, predict, Row, Status } from "."
    import {
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "../../single-page/components/tags-view"
    import DetailList from "./components/detail-list.vue"

    @Component({
        name: routesMap.recruit.positionCollectedDetail,
        components: { DetailRowCol, CommonPop, DetailList },
    })
    export default class PositionCollectedDetail extends Vue {
        private id = ""
        private row: Row | null = null
        private showEdit = false
        private showPop = false

        refreshConfig = {
            fun: this.init,
            name: routesMap.recruit.positionCollectedDetail,
        }

        private get commonPopConfig(): CommonPopConfig {
            if (!this.row) return {}
            return {
                id: this.row!.id + "",
                sdkModel: "xg_position_collected",
                sdkAction: "update",
                title: "修改岗位信息",
                labelWidth: "120px",
                formWidth: "1200px",
            }
        }

        private get showCompanyInfo() {
            return this.row?.xg_agent_contact_person
        }

        private get showOddInfo() {
            return (
                this.row?.odd_job_name ||
                this.row?.object_data?.AAE004 ||
                this.row?.object_data?.AAE005
            )
        }

        private get handlePopConfig(): CommonPopConfig | null {
            return handlePopConfig(this.row)
        }

        breadcrumbs: BreadcrumbItem[] = []
        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                ...getCacheBreadcrumbsByRoutePath(this.from),
                {
                    label: "岗位筹集详情",
                    to: {
                        name: routesMap.recruit.positionCollectedDetail,
                        query: {
                            id: this.id + "",
                        },
                    },
                },
            ]
            // console.log("d", JSON.parse(JSON.stringify(d)))
            updateTagItem({
                name: routesMap.recruit.positionCollectedDetail,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        private get from() {
            return this.$route.query.from as string
        }

        private get labelStyle() {
            return {
                minWidth: "126px",
                textAlign: "right",
                marginRight: "10px",
                color: "#90912A6",
            }
        }

        mounted() {
            this.init()
        }

        private init() {
            this.id = this.$route.query.id as string
            this.row = null
            this.setBreadcrumbs()
            pageLoading(() => {
                return sdk.core
                    .model("xg_position_collected")
                    .detail(this.id, "manage")
                    .query()
                    .then((r) => {
                        this.row = sdk.buildRow<Row>(r.row, predict)
                        console.log("row", JSON.parse(JSON.stringify(this.row)))
                    })
            })
        }

        private toEdit() {
            this.$router.push({
                name: routesMap.recruit.positionCollectedAdd,
                query: {
                    id: this.row!.id + "",
                    from: routesMap.recruit.positionCollectedDetail,
                },
            })
        }

        private get isFullTime() {
            return this.row?.position_type === PositionType.全职
        }

        private get items(): Array<ColItem & { hidden?: boolean }> {
            if (!this.row) return []
            const h = this.$createElement
            const imgArr = (this.row.pic_url || "")
                .split(",")
                .filter((i) => i)
                .map((i: string) => sdk.buildImage(i))
            return [
                {
                    label: "岗位名称",
                    value: this.row.name,
                    span: 8,
                },
                {
                    label: "岗位类型",
                    value: this.row.position_type_label,
                    span: 8,
                },
                {
                    label: "工作地",
                    value: this.row.address_detail,
                    span: 8,
                },
                {
                    label: "岗位所属企业",
                    value: this.row.company_name
                        ? this.row.company_name +
                          ` (${this.row.tag_agent ? "已入驻" : "未入驻"})`
                        : "",
                    span: 8,
                    hidden: !this.isFullTime,
                },
                {
                    label: "岗位所属雇主",
                    vNode: h(
                        "div",
                        { class: "u-flex u-flex-wrap" },
                        [
                            h("div", { class: "u-m-r-12" }, [
                                renDesensitizationView2Remote(h, {
                                    modelName: "xg_position_collected",
                                    actionName: "show_contact_mobile",
                                    rowId: this.row.id + "",
                                    value: this.row.contact_mobile,
                                    handlerDisplay: (display: string) => {
                                        return `${this.row!.contact_person || ""} ${
                                            display ? `(${display})` : ""
                                        }`
                                    },
                                }),
                            ]),
                            this.row.company_name
                                ? h(
                                      "div",
                                      this.row.company_name +
                                          ` (${
                                              this.row.tag_agent
                                                  ? "已入驻"
                                                  : "未入驻"
                                          })`
                                  )
                                : undefined,
                        ].filter(Boolean)
                    ),
                    span: 8,
                    hidden: this.isFullTime,
                },
                {
                    label: "招工人数",
                    value: this.row.recruit_count
                        ? this.row.recruit_count + "人"
                        : "--",
                    span: 8,
                },
                // {
                //     label: "企业联系人",
                //     value: this.row.xg_agent_contact_person,
                //     span: 8,
                // },
                // {
                //     label: "企业联系人电话",
                //     vNode: renDesensitizationView(h, {
                //         value: this.row.xg_agent_contact_mobile,
                //         handlerDisplay: (display: string) => {
                //             return `123${display}`
                //         },
                //     }),
                //     span: 8,
                // },
                // {
                //     label: "是否入驻",
                //     value: this.row.tag_agent_label,
                //     span: 8,
                // },
                // {
                //     label: "企业联系人",
                //     value: this.row.contact_person,
                //     span: 8,
                // },
                {
                    label: "岗位薪资",
                    value: this.row.salary_label,
                    span: 8,
                },
                {
                    label: "联系信息",
                    vNode: renDesensitizationView2Remote(h, {
                        modelName: "xg_position_collected",
                        actionName: "show_contact_mobile",
                        rowId: this.row.id + "",
                        value: this.row.contact_mobile,
                        handlerDisplay: (display: string) => {
                            return `${this.row!.contact_person || ""} (${display})`
                        },
                    }),
                    hidden: this.isFullTime,
                    span: 8,
                },
                {
                    label: "岗位描述",
                    value: this.row.description,
                    span: 24,
                },
                // {
                //     label: "工作环境",
                //     vNode: imgArr.length
                //         ? h("div", { class: "u-flex u-col-top" }, [
                //               h("el-image", {
                //                   props: {
                //                       "preview-src-list": [...imgArr],
                //                       fit: "contain",
                //                       src: imgArr.length ? imgArr[0] : "",
                //                   },
                //                   style: {
                //                       width: "80px",
                //                       height: "80px",
                //                       display: !imgArr.length && "none",
                //                   },
                //               }),
                //           ])
                //         : h("span", "--"),
                //     span: 24,
                // },
            ].filter((e) => !e.hidden)
        }

        private get items2(): Array<ColItem & { hidden?: boolean }> {
            if (!this.row) return []
            const h = this.$createElement
            return [
                // {
                //     label: "状态",
                //     value: this.row.status_label,
                //     span: 12,
                // },
                // {
                //     label: "零工驿站名称",
                //     value: this.row.odd_job_name,
                //     span: 12,
                // },
                {
                    label: "所属网格区域",
                    value: this.row.region_name,
                    span: 24,
                },
                // {
                //     label: "网格员姓名",
                //     value: this.row.real_name,
                //     span: 12,
                // },
                {
                    label: "网格员电话",
                    vNode: renDesensitizationView(h, {
                        value: this.row.mobile,
                        handlerDisplay: (display: string) => {
                            return `${this.row!.real_name || ""} (${display})`
                        },
                    }),
                    span: 12,
                },
            ]
        }

        private get items3(): Array<ColItem & { hidden?: boolean }> {
            if (!this.row) return []
            return [
                {
                    label: "处理状态",
                    value:
                        this.row.status_label +
                        (this.row.status === Status.不通过
                            ? ` (${this.row.audit_memo})`
                            : ""),
                    span: 12,
                },
            ]
        }

        private get items4(): Array<ColItem & { hidden?: boolean }> {
            if (!this.row) return []
            const h = this.$createElement
            return [
                {
                    label: "岗位所属企业",
                    value:
                        this.row.company_name +
                        ` (${this.row.tag_agent ? "已入驻" : "未入驻"})`,
                    span: 248,
                },
                // {
                //     label: "企业联系人",
                //     value: this.row.xg_agent_contact_person,
                //     span: 8,
                // },
                {
                    label: "联系信息",
                    vNode: renDesensitizationView(h, {
                        value: this.row.xg_agent_contact_mobile,
                        handlerDisplay: (display: string) => {
                            return `${
                                this.row!.xg_agent_contact_person || ""
                            } (${display})`
                        },
                    }),
                    span: 24,
                },
            ]
        }

        private get items5(): Array<ColItem & { hidden?: boolean }> {
            if (!this.row) return []
            const h = this.$createElement
            return [
                {
                    label: "所属驿站",
                    value: this.row.odd_job_name,
                    span: 12,
                },
                // {
                //     label: "企业联系人",
                //     value: this.row.xg_agent_contact_person,
                //     span: 8,
                // },
                {
                    label: "联系信息",
                    vNode: renDesensitizationView(h, {
                        value: this.row.object_data?.AAE005,
                        handlerDisplay: (display: string) => {
                            return `${
                                this.row!.object_data?.AAE004 || ""
                            } (${display})`
                        },
                    }),
                    span: 24,
                },
            ]
        }

        private sendAgentSMS() {
            pageLoading(() => {
                return sdk.core
                    .model("xg_position_collected")
                    .action("send_release_sms")
                    .updateInitialParams({
                        selected_list: [{ v: 0, id: this.row!.id }],
                    })
                    .execute()
                    .then(() => {
                        this.$message.success("发送成功")
                        this.init()
                    })
            })
        }

        private sendAgentSMS2() {
            pageLoading(() => {
                return sdk.core
                    .model("xg_position_collected")
                    .action("send_sms")
                    .updateInitialParams({
                        selected_list: [{ v: 0, id: this.row!.id }],
                    })
                    .execute()
                    .then(() => {
                        this.$message.success("发送成功")
                        this.init()
                    })
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .title {
        width: 100%;
        height: 40px;
        background: #f8f8f8;
        color: #222;
        font-size: 18px;
        font-weight: 600;
        line-height: 40px;
        padding: 0 20px;
    }
</style>
