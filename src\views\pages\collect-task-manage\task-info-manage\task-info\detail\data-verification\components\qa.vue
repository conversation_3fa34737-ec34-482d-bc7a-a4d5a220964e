<template>
    <el-dialog
        class="import-dialog"
        append-to-body
        :visible="value"
        @close="close"
        :close-on-click-modal="true"
        :width="width"
        :title="'外呼问卷'"
    >
        <!-- Chat-style conversation list -->
        <div class="conversation-section">
            <h3>对话记录</h3>
            <div v-if="messages.length > 0" class="message-list">
                <div
                    v-for="(msg, index) in messages"
                    :key="index"
                    :class="['message', msg.speaker]"
                >
                    <div class="message-header">
                        <span class="speaker">{{
                            msg.speaker === "system" ? "系统" : "用户"
                        }}</span>
                        <span class="time">{{ msg.time }}</span>
                    </div>
                    <div class="message-content">{{ msg.content }}</div>
                </div>
            </div>
            <div v-else class="no-data">暂无对话数据</div>
        </div>

        <!-- Summary section -->
        <div class="summary-section u-m-t-30">
            <h3>意向分析</h3>
            <div
                v-if="Object.keys(intentionAnalysisEvaluationBasis).length > 0"
                class="summary-content"
            >
                <p>
                    <strong>意向分析:</strong>
                    {{ intentionAnalysisEvaluationBasis.意向分析 }}
                </p>
                <p>
                    <strong>意向等级:</strong>
                    {{ intentionAnalysisEvaluationBasis.意向等级 }}
                </p>
                <p>
                    <strong>违规判断:</strong>
                    {{ intentionAnalysisEvaluationBasis.违规判断 }}
                </p>
            </div>
            <div v-else class="no-data">暂无分析数据</div>
        </div>
    </el-dialog>
</template>

<script lang="ts">
import {
    DialogContainerSize,
    DialogController,
} from "@/core-ui/controller/dialog-controller"
import { Component, Mixins, Prop } from "vue-property-decorator"

@Component({})
export default class QaContainer extends Mixins(DialogController) {
    private width = DialogContainerSize.containerSize

    @Prop()
    private data!: any

    get messages() {
        if (!this.data || !this.data.record) {
            return []
        }

        const messages = []
        for (const record of this.data.record) {
            if (record.request_content) {
                messages.push({
                    time: record.dialog_time,
                    speaker: "system",
                    content: record.request_content,
                })
            }
            if (record.response_content) {
                messages.push({
                    time: record.dialog_time,
                    speaker: "user",
                    content: record.response_content,
                })
            }
        }
        return messages
    }

    get intentionAnalysisEvaluationBasis() {
        if (!this.data || !this.data.intentionAnalysisEvaluationBasis) {
            return {}
        }
        try {
            return JSON.parse(
                this.data?.intentionAnalysisEvaluationBasis.result || "{}"
            )
        } catch (e) {
            console.error("解析意向分析数据失败:", e)
            return {}
        }
    }
}
</script>

<style lang="less" scoped>
.import-dialog {
    ::v-deep .el-dialog__body {
        padding: 20px;
    }

    .conversation-section,
    .summary-section {
        h3 {
            font-size: 16px;
            margin-bottom: 15px;
            color: #333;
            font-weight: bold;
        }
    }

    .message-list {
        max-height: 400px;
        overflow-y: auto;
        border: 1px solid #ebeef5;
        border-radius: 4px;
        padding: 10px;
    }

    .message {
        margin-bottom: 15px;
        padding: 10px;
        border-radius: 4px;

        &.system {
            background-color: #f0f7ff;
            border-left: 3px solid #409eff;
        }

        &.user {
            background-color: #f6ffed;
            border-left: 3px solid #52c41a;
        }

        .message-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 12px;
            color: #666;

            .speaker {
                font-weight: bold;
            }
        }

        .message-content {
            line-height: 1.5;
        }
    }

    .summary-content {
        background: #f9f9f9;
        padding: 15px;
        border-radius: 4px;
        border-left: 4px solid #409eff;

        p {
            margin: 8px 0;
            line-height: 1.5;
        }
    }

    .no-data {
        text-align: center;
        padding: 20px;
        color: #999;
        font-size: 14px;
    }
}
</style>
