<template>
    <div>
        <slot v-if="cur === 0" />
        <template v-if="all">
            <el-checkbox
                v-if="cur === 1"
                :value="true"
                :disabled="true"
            ></el-checkbox>
            <div class="pointer">
                <div @click="change">{{ list[cur].label }}</div>
            </div>
        </template>
    </div>
</template>

<script lang='ts'>
    import { Component, Prop, Vue } from "vue-property-decorator"
    import { SelectionType } from "./index"

    @Component({ name: "selection-header", components: {} })
    export default class SelectionHeader extends Vue {
        @Prop({
            default: false,
        })
        all!: boolean
        @Prop()
        column!: any

        cur = SelectionType.本页
        list = [
            {
                label: "本页",
                key: SelectionType.本页,
            },
            {
                label: "全部",
                key: SelectionType.全部,
            },
        ]

        change() {
            this.cur = (this.cur + 1) % 2
            this.$emit("selectionTypeChange", this.cur)
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
</style>
