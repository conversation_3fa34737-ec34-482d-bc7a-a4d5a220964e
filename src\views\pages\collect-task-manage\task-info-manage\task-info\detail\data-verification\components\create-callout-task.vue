<template>
    <el-dialog
        class="import-dialog"
        append-to-body
        :visible="value"
        @close="close"
        :close-on-click-modal="true"
        :width="width"
        :title="'添加外呼任务'"
    >
        <div class="form-container">
            <el-form :model="formData" label-width="100px" class="callout-form">
                <!-- 任务名称 -->
                <el-form-item label="任务名称:">
                    <el-input
                        v-model="formData.name"
                        placeholder="请设置名称"
                        style="width: 400px"
                    ></el-input>
                </el-form-item>

                <!-- 任务描述 -->
                <el-form-item label="任务描述:">
                    <el-input
                        v-model="formData.variables"
                        type="textarea"
                        placeholder="请设置描述"
                        :rows="4"
                        style="width: 400px"
                    ></el-input>
                </el-form-item>

                <!-- 任务时间 -->
                <el-form-item label="任务时间:">
                    <div class="date-range-container">
                        <el-date-picker
                            v-model="formData.begin_time"
                            type="date"
                            placeholder="2025-03-01"
                            value-format="yyyy-MM-dd"
                            style="width: 150px"
                        ></el-date-picker>
                        <span class="date-separator">~</span>
                        <el-date-picker
                            v-model="formData.end_time"
                            type="date"
                            placeholder="2025-03-01"
                            value-format="yyyy-MM-dd"
                            style="width: 150px"
                        ></el-date-picker>
                    </div>
                </el-form-item>

                <!-- 外呼时间段 -->
                <el-form-item label="外呼时间段:">
                    <div class="time-range-container">
                        <div class="time-period">
                            <span class="period-label">上午:</span>
                            <el-select
                                v-model="amStartTime"
                                placeholder="请选择开始时间"
                                style="width: 120px; margin-right: 10px"
                            >
                                <el-option
                                    v-for="time in timeOptions"
                                    :key="time"
                                    :label="time"
                                    :value="time"
                                ></el-option>
                            </el-select>
                            <el-select
                                v-model="amEndTime"
                                placeholder="请选择结束时间"
                                style="width: 120px"
                            >
                                <el-option
                                    v-for="time in timeOptions"
                                    :key="time"
                                    :label="time"
                                    :value="time"
                                ></el-option>
                            </el-select>
                        </div>
                        <div class="time-period">
                            <span class="period-label">下午:</span>
                            <el-select
                                v-model="pmStartTime"
                                placeholder="请选择开始时间"
                                style="width: 120px; margin-right: 10px"
                            >
                                <el-option
                                    v-for="time in timeOptions"
                                    :key="time"
                                    :label="time"
                                    :value="time"
                                ></el-option>
                            </el-select>
                            <el-select
                                v-model="pmEndTime"
                                placeholder="请选择结束时间"
                                style="width: 120px"
                            >
                                <el-option
                                    v-for="time in timeOptions"
                                    :key="time"
                                    :label="time"
                                    :value="time"
                                ></el-option>
                            </el-select>
                        </div>
                    </div>
                </el-form-item>

                <!-- 外呼数量 -->
                <!-- <el-form-item label="外呼数量:">
                    <span class="callout-count">1023人</span>
                </el-form-item> -->
            </el-form>
        </div>

        <div slot="footer" class="dialog-footer">
            <el-button @click="close">取消</el-button>
            <el-button type="primary" @click="handleConfirm">保存</el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts">
import {
    DialogContainerSize,
    DialogController,
} from "@/core-ui/controller/dialog-controller"
import { Component, Mixins } from "vue-property-decorator"

interface FormData {
    name: string
    variables: string
    begin_time: string
    end_time: string
    am_range: string
    pm_range: string
}

@Component({})
export default class CreateCalloutTask extends Mixins(DialogController) {
    private width = DialogContainerSize.containerSize

    // 表单数据
    private formData: FormData = {
        name: "",
        variables: "",
        begin_time: "",
        end_time: "",
        am_range: "",
        pm_range: "",
    }

    // 上午时间段
    private amStartTime = "08:30"
    private amEndTime = "11:30"

    // 下午时间段
    private pmStartTime = "14:30"
    private pmEndTime = "20:30"

    // 时间选项
    private timeOptions = [
        "08:00",
        "08:30",
        "09:00",
        "09:30",
        "10:00",
        "10:30",
        "11:00",
        "11:30",
        "12:00",
        "12:30",
        "13:00",
        "13:30",
        "14:00",
        "14:30",
        "15:00",
        "15:30",
        "16:00",
        "16:30",
        "17:00",
        "17:30",
        "18:00",
        "18:30",
        "19:00",
        "19:30",
        "20:00",
        "20:30",
        "21:00",
    ]

    // 确定按钮点击事件
    private handleConfirm() {
        // 更新时间范围
        this.formData.am_range = `${this.amStartTime}-${this.amEndTime}`
        this.formData.pm_range = `${this.pmStartTime}-${this.pmEndTime}`

        // 打印表单数据
        console.log("表单数据:", this.formData)

        // 关闭弹窗
        this.close()
    }

    // 初始化时间选择器的值
    created() {
        // 解析上午时间段
        if (this.formData.am_range) {
            const [amStart, amEnd] = this.formData.am_range.split("-")
            this.amStartTime = amStart
            this.amEndTime = amEnd
        }

        // 解析下午时间段
        if (this.formData.pm_range) {
            const [pmStart, pmEnd] = this.formData.pm_range.split("-")
            this.pmStartTime = pmStart
            this.pmEndTime = pmEnd
        }
    }
}
</script>

<style lang="less" scoped>
.form-container {
    padding: 20px;
}

.callout-form {
    .el-form-item {
        margin-bottom: 20px;

        .el-form-item__label {
            font-weight: normal;
            color: #333;
        }
    }
}

.date-range-container {
    display: flex;
    align-items: center;

    .date-separator {
        margin: 0 10px;
        color: #666;
    }
}

.time-range-container {
    .time-period {
        display: flex;
        align-items: center;
        margin-bottom: 10px;

        .period-label {
            width: 40px;
            color: #333;
            font-weight: normal;
        }

        &:last-child {
            margin-bottom: 0;
        }
    }
}

.callout-count {
    color: #333;
    font-size: 14px;
}

.dialog-footer {
    text-align: center;
    padding: 10px 0;

    .el-button {
        min-width: 80px;
    }
}
</style>
