<template>
    <el-dialog
        class="import-dialog"
        append-to-body
        :visible="value"
        @close="close"
        :close-on-click-modal="true"
        :width="width"
        :title="'添加外呼任务'"
    >
    </el-dialog>
</template>

<script lang="ts">
    import {
        DialogContainerSize,
        DialogController,
    } from "@/core-ui/controller/dialog-controller"
    import { Component, Mixins } from "vue-property-decorator"

    @Component({})
    export default class CreateCalloutTask extends Mixins(DialogController) {
        private width = DialogContainerSize.containerSize
    }
</script>

<style lang="less" scoped>
</style>
