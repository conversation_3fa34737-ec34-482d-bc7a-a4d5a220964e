import { cloneDeep, assign } from "lodash"
import XLSXS from "xlsx-js-style"

interface MergeCell {
    r: number
    c: number
}
export interface ExcelGeneratorOption {
    fileName: string
    columns: (string | undefined)[]
    childColumns?: (string | undefined)[]
    rows: (string | number | undefined)[][]

    /**
     * 是否自动适配单元格宽度，默认为 true
     */
    autofit?: boolean

    /**
     * 只生成内存级别文件，不触发自动下载
     */
    toBinary?: boolean
    primaryRows?: any[]
    mergeCells?: { s: MergeCell; e: MergeCell }[]
    colWidths?: number[]
}

export class ExcelGenerator {
    private static readonly defaultWidth = 3
    private static readonly doubleCharReg =
        /[\u2E80-\u2EFF\u2F00-\u2FDF\u3000-\u303F\u31C0-\u31EF\u3200-\u32FF\u3300-\u33FF\u3400-\u4DBF\u4DC0-\u4DFF\u4E00-\u9FBF\uF900-\uFAFF\uFE30-\uFE4F\uFF00-\uFFEF]+/g

    private static getLength(s: string) {
        let l = 0
        for (let i = 0; i < s.length; i++) {
            l += this.doubleCharReg.test(s.charAt(i)) ? 2 : 1.2
        }
        return l
    }

    public static execute(option: ExcelGeneratorOption) {
        const primay = _.map(option.primaryRows, (i) => {
            return [i]
        })
        let datas = [...primay, option.columns, ...option.rows]
        if (option.childColumns?.length) {
            datas = [
                ...primay,
                option.columns,
                option.childColumns,
                ...option.rows,
            ]
        }
        const lengths: number[] = _.map(option.columns, (i) => {
            return this.getLength(i || "") || this.defaultWidth
        })

        for (const item of option.rows) {
            for (let i = 0; i < item.length; i++) {
                const cell = item[i]
                const m =
                    _.isString(cell) || _.isNumber(cell)
                        ? this.getLength(cell + "")
                        : this.defaultWidth
                if (m > lengths[i]) {
                    lengths[i] = m
                }
            }
        }

        const wsName = "Sheet1"
        const wb = XLSXS.utils.book_new()
        const ws = XLSXS.utils.aoa_to_sheet(datas)

        // 合并单元格
        const mergeArr = []
        for (let i = 0; i < option.columns.length; i++) {
            if (option.columns[i] !== "" && option.columns[i + 1] === "") {
                const startIndex = i
                let endIndex = i + 1
                // 找到连续的空值的最后一个索引
                while (option.columns[endIndex] === "") {
                    endIndex++
                }
                mergeArr.push({
                    s: { r: 0 + primay?.length, c: startIndex },
                    e: { r: 0 + primay?.length, c: endIndex - 1 },
                })
            } else if (
                option.columns[i] !== "" &&
                option.columns[i + 1] !== ""
            ) {
                mergeArr.push({
                    s: { r: 0 + primay?.length, c: i },
                    e: { r: 1 + primay?.length, c: i },
                })
            }
        }
        if (option.childColumns?.length) {
            ws["!merges"] = mergeArr
        } else if (option.mergeCells?.length) {
            ws["!merges"] = [...(option.mergeCells || [])]
        }
        if (option.autofit !== false) {
            const colWidths =
                option.colWidths ||
                option.columns.map((_, i) => {
                    // 计算每列中内容的最大宽度
                    const lengths = datas.map((row) =>
                        row[i] ? row[i].toString().length * 3 : 15
                    )
                    return Math.max(...lengths)
                })

            // 设置每列的宽度
            ws["!cols"] = colWidths.map((width: number) => ({ width }))
        }

        // 设置表头单元格样式
        const numColumns = option.childColumns?.length || option.columns?.length
        const headerRowCount = option.childColumns?.length
            ? 2 + primay?.length
            : 1 + primay?.length
        const headerCellStyle = {
            alignment: {
                vertical: "center",
                horizontal: "center",
            },
            font: {
                bold: false,
                sz: 12,
            },
            border: {
                top: { style: "thin", color: { rgb: "000000" } },
                bottom: { style: "thin", color: { rgb: "000000" } },
                left: { style: "thin", color: { rgb: "000000" } },
                right: { style: "thin", color: { rgb: "000000" } },
            },
        }

        if (primay?.length) {
            for (let r = 0; r < primay.length; r++) {
                for (let c = 0; c < numColumns; c++) {
                    const cellAddress = XLSXS.utils.encode_cell({ r, c })
                    if (!ws[cellAddress]) {
                        ws[cellAddress] = { t: "s", v: "", s: {} }
                    }
                    ws[cellAddress].s = assign(
                        {
                            font: {
                                bold: true,
                                sz: 12,
                            },
                        },
                        ws[cellAddress].s
                    )
                }
            }
        }

        for (let r = primay?.length ?? 0; r < headerRowCount; r++) {
            for (let c = 0; c < numColumns; c++) {
                const cellAddress = XLSXS.utils.encode_cell({ r, c })
                if (!ws[cellAddress]) {
                    ws[cellAddress] = { t: "s", v: "", s: {} }
                }
                ws[cellAddress].s = { ...headerCellStyle, ...ws[cellAddress].s }
            }
        }
        for (let c = 0; c < numColumns; c++) {
            const cellAddress = XLSXS.utils.encode_cell({
                r: headerRowCount,
                c,
            })
            if (!ws[cellAddress]) {
                ws[cellAddress] = { t: "s", v: "", s: {} }
            }
        }

        XLSXS.utils.book_append_sheet(wb, ws, wsName)

        const name = `${option.fileName}.xlsx`

        if (option.toBinary) {
            const o = XLSXS.write(wb, {
                bookType: "xlsx",
                type: "buffer",
            }) as string
            return this.toFile(o, name)
        }

        XLSXS.writeFile(wb, name)
        return null
    }

    public static execute2Style(option: ExcelGeneratorOption) {
        const primay = _.map(option.primaryRows, (i) => {
            return [i]
        })
        let datas = [...primay, option.columns, ...option.rows]
        if (option.childColumns?.length) {
            datas = [
                ...primay,
                option.columns,
                option.childColumns,
                ...option.rows,
            ]
        }
        const lengths: number[] = _.map(option.columns, (i) => {
            return this.getLength(i || "") || this.defaultWidth
        })

        for (const item of option.rows) {
            for (let i = 0; i < item.length; i++) {
                const cell = item[i]
                const m =
                    _.isString(cell) || _.isNumber(cell)
                        ? this.getLength(cell + "")
                        : this.defaultWidth
                if (m > lengths[i]) {
                    lengths[i] = m
                }
            }
        }

        const wsName = "Sheet1"
        const wb = XLSXS.utils.book_new()
        const ws = XLSXS.utils.aoa_to_sheet(datas)

        // 合并单元格
        const mergeArr = []
        for (let i = 0; i < option.columns.length; i++) {
            if (option.columns[i] !== "" && option.columns[i + 1] === "") {
                const startIndex = i
                let endIndex = i + 1
                // 找到连续的空值的最后一个索引
                while (option.columns[endIndex] === "") {
                    endIndex++
                }
                mergeArr.push({
                    s: { r: 0 + primay?.length, c: startIndex },
                    e: { r: 0 + primay?.length, c: endIndex - 1 },
                })
            } else if (
                option.columns[i] !== "" &&
                option.columns[i + 1] !== ""
            ) {
                mergeArr.push({
                    s: { r: 0 + primay?.length, c: i },
                    e: { r: 1 + primay?.length, c: i },
                })
            }
        }
        if (option.childColumns?.length) {
            ws["!merges"] = mergeArr
        } else if (option.mergeCells?.length) {
            ws["!merges"] = [...(option.mergeCells || [])]
        }
        if (option.autofit !== false) {
            const colWidths =
                option.colWidths ||
                option.columns.map((_, i) => {
                    // 计算每列中内容的最大宽度
                    const lengths = datas.map((row) =>
                        row[i] ? row[i].toString().length * 3 : 15
                    )
                    return Math.max(...lengths)
                })

            // 设置每列的宽度
            ws["!cols"] = colWidths.map((width: number) => ({ width }))
        }

        // 设置表头单元格样式
        const numColumns = option.childColumns?.length || option.columns?.length
        const headerRowCount = option.childColumns?.length
            ? 2 + primay?.length
            : 1 + primay?.length
        const headerCellStyle = {
            alignment: {
                vertical: "center",
                horizontal: "center",
            },
            font: {
                bold: false,
                sz: 12,
            },
            border: {
                top: { style: "thin", color: { rgb: "000000" } },
                bottom: { style: "thin", color: { rgb: "000000" } },
                left: { style: "thin", color: { rgb: "000000" } },
                right: { style: "thin", color: { rgb: "000000" } },
            },
        }

        if (primay?.length) {
            for (let r = 0; r < primay.length; r++) {
                for (let c = 0; c < numColumns; c++) {
                    const cellAddress = XLSXS.utils.encode_cell({ r, c })
                    if (!ws[cellAddress]) {
                        ws[cellAddress] = { t: "s", v: "", s: {} }
                    }
                    ws[cellAddress].s = assign(
                        {
                            font: {
                                bold: true,
                                sz: 12,
                            },
                        },
                        ws[cellAddress].s
                    )
                }
            }
        }

        for (let r = primay?.length ?? 0; r < headerRowCount; r++) {
            for (let c = 0; c < numColumns; c++) {
                const cellAddress = XLSXS.utils.encode_cell({ r, c })
                if (!ws[cellAddress]) {
                    ws[cellAddress] = { t: "s", v: "", s: {} }
                }
                ws[cellAddress].s = { ...headerCellStyle, ...ws[cellAddress].s }
            }
        }

        // 处理所有数据单元格，设置垂直居中，对于包含换行符的单元格还设置自动换行
        const dataStartRow = headerRowCount
        for (let r = dataStartRow; r < datas.length; r++) {
            for (let c = 0; c < numColumns; c++) {
                const cellAddress = XLSXS.utils.encode_cell({ r, c })
                if (!ws[cellAddress]) {
                    ws[cellAddress] = { t: "s", v: "", s: {} }
                }

                // 确保单元格样式对象存在
                if (!ws[cellAddress].s) {
                    ws[cellAddress].s = {}
                }

                // 确保alignment对象存在
                if (!ws[cellAddress].s.alignment) {
                    ws[cellAddress].s.alignment = {}
                }

                // 所有数据单元格都设置垂直居中
                ws[cellAddress].s.alignment.vertical = "center"

                // 获取单元格值
                const cellValue = ws[cellAddress].v

                // 检查单元格是否包含换行符，如果包含则设置自动换行并调整行高
                if (typeof cellValue === "string" && cellValue.includes("\n")) {
                    ws[cellAddress].s.alignment.wrapText = true

                    // 调整行高
                    if (!ws["!rows"]) {
                        ws["!rows"] = []
                    }

                    // 估计行高 - 计算换行符数量加1乘以行高基准值
                    const lineCount = (cellValue.match(/\n/g) || []).length + 1
                    const rowHeight = Math.max(lineCount * 15, 20) // 基准行高为15，最小20

                    if (!ws["!rows"][r]) {
                        ws["!rows"][r] = { hpt: rowHeight } // hpt是点单位的高度
                    } else {
                        ws["!rows"][r].hpt = Math.max(
                            ws["!rows"][r].hpt || 0,
                            rowHeight
                        )
                    }
                }
            }
        }

        XLSXS.utils.book_append_sheet(wb, ws, wsName)

        const name = `${option.fileName}.xlsx`

        if (option.toBinary) {
            const o = XLSXS.write(wb, {
                bookType: "xlsx",
                type: "buffer",
            }) as string
            return this.toFile(o, name)
        }

        XLSXS.writeFile(wb, name)
        return null
    }

    private static s2ab(s: string) {
        const buf = new ArrayBuffer(s.length)
        const view = new Uint8Array(buf)
        for (let i = 0; i !== s.length; ++i) view[i] = s.charCodeAt(i) & 0xff
        return buf
    }

    public static toFile(content: string, name: string) {
        const blob = new Blob([this.s2ab(content)])
        return new File([blob], name)
    }
}
