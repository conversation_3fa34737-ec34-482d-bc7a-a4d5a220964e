import { tableFilterConfigGenerator } from "@/core-ui/component/form"
import { TableColumn } from "@/core-ui/component/table"
import { sdk } from "@/service"
import { field_group } from "uniplat-sdk"
import { renDesensitizationView2Remote } from "@/views/components/common-comps"
import { cloneDeep, get } from "lodash"
import { componentsMsgService } from "@/core-ui/component/component-msg-service"

export function buildConfig4Remote(
    modelName: string,
    listName: string,
    prefilters?: any,
    disabledOpt = false,
    disabledFilter = false,
    params?: {
        predicts: Record<string, string>
    }
) {
    const predicts = params?.predicts || {}
    return getIntentSearchMeta(modelName, listName).then((t) => {
        const fieldGroups = t.pageData.meta.field_groups || []

        const configResource = fieldGroups.map((i, idx) => {
            // template可能是多个值，需要解析出来
            const info = formateTemplate(i.template)
            Object.assign(predicts, {
                ...info.predicts,
            })
            const mapKeys = [...info.mapKeys]

            return {
                label: i.label,
                mapKeys: mapKeys,
                source: i,
                idx: idx,
            }
        })
        const columns = buildColumns(configResource)
        if (!disabledOpt) {
            columns.push({
                label: "操作",
                prop: "h",
                width: "200",
            })
        }
        return {
            meta: t.pageData.meta,
            tableConfig: {
                model: sdk.core.model(modelName).list(listName),
                useRemoteFilter: !disabledFilter,
                defaultPageSize: 10,
                predict: predicts,
                preFilter: prefilters || [],
                oneTabFilter: true,
                filter: [],
            },
            filter: tableFilterConfigGenerator(t),
            columns: columns,
        }
    })
}

export function buildConfig4RemoteMeta(
    modelName: string,
    listName: string,
    params?: {
        prefilters?: any // 过滤条件
        disabledFilter?: boolean // 为false，则table的筛选自动添加，无法自定义。为true，会将数据抛出，在外面可以自定义添加进tableConfig
        predicts?: Record<string, string> // 添加额外的自定义格式化模版
        disabledOpt?: boolean // 是否显示操作列
        optColumn?: TableColumn<any> // 自定义操作列
        addSelector?: boolean // 是否显示选择器
        selectable?: (row: any) => boolean // 是否显示选择器显示条件
        isSingleSelector?: boolean // 是否是多选显示选择器
        useLabelWidth?: boolean // 使用远程的label宽度
        customLabelWidths?: Record<string, number> // 自定义label宽度，如果同时使用远程，则对比使用两个中最长的一个宽度
        useTabs?: boolean // 如果返回是tabs，则从第一个tabs里面读取配置。
        useTabsV2?: boolean // useTabs的升级版本，里面的column和过滤和格式化模版全部一一对应拿到，不过外面需要处理
        useRowFieldGroups?: boolean // 使用返回的field_groups的进行直接渲染，前端不做处理
        anonymous?: boolean
    }
) {
    const disabledOpt = params?.disabledOpt || false
    const disabledFilter = params?.disabledFilter || false
    const predicts = params?.predicts || {}
    const useLabelWidth = params?.useLabelWidth || false
    const useTabs = params?.useTabs || false
    const optColumn = params?.optColumn || {
        label: "操作",
        prop: "h",
        fixed: "right",
    }
    const addSelector = params?.addSelector || false
    const isSingleSelector = params?.isSingleSelector || false
    const useRowFieldGroups = params?.useRowFieldGroups || false
    const customLabelWidths = params?.customLabelWidths || null
    const useTabsV2 = params?.useTabsV2 || false
    const selectable = params?.selectable || undefined

    // 先构建基础的样式，此时的传参后续不会变化。
    const buildColumnsAndPredicts = buildViewsConfig({
        useLabelWidth: useLabelWidth,
        useRowFieldGroups: useRowFieldGroups,
        customLabelWidths: customLabelWidths,
        modelName: modelName,
        disabledOpt: disabledOpt,
        optColumn: optColumn,
        addSelector: addSelector,
        isSingleSelector: isSingleSelector,
        selectable: selectable,
    })
    const prefilters = params?.prefilters || {}
    return getIntentSearchMeta(
        modelName,
        listName,
        prefilters,
        params?.anonymous
    ).then((t) => {
        const meta = t.pageData.meta
        const tabFieldGroups = meta.pages?.map((i) => i.field_groups) || []
        // 最终返回的数据结构的函数
        const buildResult = (p: {
            predicts: Record<string, string>
            columns: TableColumn<any>[]
            preFilter: any
            extraObj?: Record<string, any>
        }) => {
            return {
                meta: meta,
                tableConfig: {
                    model: sdk.core.model(modelName).list(listName),
                    useRemoteFilter: !disabledFilter,
                    defaultPageSize: 10,
                    predict: p.predicts,
                    preFilter: p.preFilter || {},
                    oneTabFilter: true,
                    filter: [],
                },
                filter: tableFilterConfigGenerator(t),
                columns: p.columns,
                ...(p.extraObj ? p.extraObj : {}),
            }
        }
        // 使用同一套的columns和predicts进行构建
        if (!useTabsV2 || !tabFieldGroups.length) {
            const mFilterGroups = meta.field_groups
            const tFilterGroups = tabFieldGroups[0] || mFilterGroups
            const fieldGroups = (useTabs ? tFilterGroups : mFilterGroups) || []
            const singleResult = buildColumnsAndPredicts({
                field: fieldGroups,
                outPredicts: predicts,
            })
            return buildResult({
                predicts: singleResult.innerPredicts,
                columns: singleResult.columns,
                preFilter: prefilters,
            })
        }
        // 使用不同tabs下面的columns和predicts进行构建
        return tabFieldGroups.map((i, idx) => {
            const t = buildColumnsAndPredicts({
                field: i,
                outPredicts: predicts,
            })
            const { innerPredicts, columns } = t
            const tabInfo = meta.pages[idx] as any
            // const tabPrefilters = {}
            // tabInfo.prefilters.forEach((i: any) => {
            //     Object.assign(tabPrefilters, {
            //         [i.property]: i.value,
            //     })
            // })
            return buildResult({
                predicts: innerPredicts,
                columns: columns,
                // preFilter: tabPrefilters,
                preFilter: prefilters,
                extraObj: {
                    tabInfo: tabInfo,
                },
            })
        })
    })
}

// 显示的样式的配置
function buildViewsConfig(p: {
    useLabelWidth: boolean
    useRowFieldGroups: boolean
    customLabelWidths: Record<string, number> | null
    modelName: string
    disabledOpt: boolean
    optColumn: TableColumn<any>
    addSelector: boolean
    isSingleSelector: boolean
    selectable?: (row: any) => boolean
}) {
    return function buildColumnsAndPredicts(inner: {
        field: field_group[]
        outPredicts: Record<string, string>
    }) {
        const innerPredicts = cloneDeep(inner.outPredicts) || {}
        const configResource = inner.field.map((i, idx) => {
            // template可能是多个值，需要解析出来
            const info = formateTemplate(i.template)
            Object.assign(innerPredicts, {
                ...info.predicts,
                field_actions: "field_actions",
                field_groups: "field_groups",
                _access_key: "_access_key",
                actions: "actions",
                intents: "intents",
            })
            const mapKeys = [...info.mapKeys]

            return {
                label: i.label,
                mapKeys: mapKeys,
                labelWidth: p.useLabelWidth ? i.label_width : 0,
                source: i,
                useRowFieldGroups: p.useRowFieldGroups,
                idx: idx,
            }
        })

        const columns = buildColumns(configResource, {
            customLabelWidths: p.customLabelWidths,
            modelName: p.modelName,
        })
        if (!p.disabledOpt) {
            columns.push(p.optColumn)
        }
        if (p.addSelector) {
            if (p.isSingleSelector) {
                columns.unshift({
                    prop: "listRadio",
                    width: "40px",
                    align: "center",
                })
            } else {
                columns.unshift({
                    type: "selection",
                    prop: "select",
                    selectable: p.selectable || undefined,
                })
            }
        }

        return {
            innerPredicts,
            columns,
        }
    }
}

export function getIntentSearchMeta(
    modelName: string,
    listName: string,
    prefilters?: any,
    anonymous = false
) {
    const model = sdk.core.model(modelName).list(listName)
    return (anonymous ? model.anonymous() : model).queryMeta({
        pageIndex: 1,
        item_size: 1,
        prefilters: Object.keys(prefilters || {}).map((i) => {
            return {
                property: i,
                value: prefilters[i],
            }
        }),
    })
}

export function formateTemplate(template: string) {
    const predicts = {}
    const regex = /{([^}]+)}/g
    let match
    const mapKeys = []
    while ((match = regex.exec(template)) !== null) {
        mapKeys.push(match[1])
        const t = match[1].replaceAll(".", "#")
        Object.assign(predicts, {
            [match[1]]: t + "_label",
        })
    }

    return {
        predicts: predicts,
        mapKeys: mapKeys,
    }
}

export function buildColumns(
    configResource: {
        label: string
        mapKeys: string[]
        labelWidth?: number
        source: field_group
        useRowFieldGroups?: boolean
    }[],
    params?: {
        customLabelWidths?: Record<string, number> | null
        modelName?: string
    }
) {
    const columns: TableColumn[] = configResource.map((i, idx) => {
        const customLabelWidth = (params?.customLabelWidths || {})[i.label] || 0

        const labelWidth = Math.max(
            (i.labelWidth || 0) * 1,
            (customLabelWidth || 0) * 1
        )
        const minWidth = labelWidth
            ? {
                  minWidth: `${labelWidth}px`,
              }
            : {}

        const render = (h: any, row: any) => {
            const tar = row.field_actions[idx][0]
            const actionName = "show_mobile"
            const showMobileActionName = tar
                ? tar.action_name === actionName
                : ""
            const title = row.field_groups[idx]?.replace(/<br\/>/g, "\n")

            if (!showMobileActionName) {
                return [
                    h("div", {
                        domProps: {
                            style: "text-overflow: ellipsis; overflow: hidden; white-space: nowrap",
                            innerHTML: escapeHTML(row.field_groups[idx]),
                            title: title,
                        },
                    }),
                ]
            }

            const cacheUId = Math.random() + ""

            return [
                renDesensitizationView2Remote(h, {
                    modelName: params?.modelName || "",
                    rowId: row.id,
                    value: row.field_groups[idx],
                    actionName: actionName,
                    cacheUId: cacheUId,
                }),
            ]
        }
        console.log("fff")

        const formatter = (row: any) => {
            const r = i.mapKeys || []
            let d = i.source.template
            r.forEach((t) => {
                d = d.replaceAll(t, row[t + "_label"] ?? "")
            })
            // console.log("d", JSON.parse(JSON.stringify(d)))
            d = d
                .replaceAll("{", "")
                .replaceAll("}", "")
                .split(/<br\/>|<br>/)
                .filter((i) => i)
                .join("/")
            return d
        }
        const displayObj = i.useRowFieldGroups ? { render } : { formatter }
        return {
            label: i.label.replaceAll("<br/>", "/"),
            prop: i.label,
            ...displayObj,
            ...minWidth,
            showOverflowTip: !i.useRowFieldGroups,
        }
    })

    return columns
}

export function getShowBtn4List(rows: any[], id: string, key: string) {
    const t = rows.find((i) => i.id.value === id)
    if (!t) {
        return false
    }
    return (
        (t.actions || []).find((i: any) => i.action_name === key) ||
        (t.intents || []).find((i: any) => i.name === key)
    )
}

export function getShowBtn4Page(r: any, key: string) {
    if (r.meta.actions && r.meta.actions.length > 0) {
        const res = !!r.meta.actions.find((i: any) => i.action_name === key)
        if (res) {
            return true
        }
    }

    if (r.meta.intents && r.meta.intents.length > 0) {
        return !!r.meta.intents.find((i: any) => i.name === key)
    }

    return false
}

function escapeHTML(html: string) {
    const tempBR = "__TEMP_BR__"
    const tempBRSlash = "__TEMP_BR_SLASH__"
    const tempBRSlash2 = "__TEMP_BR_SLASH2__"
    if (
        html.includes("<br>") ||
        html.includes("<br/>") ||
        html.includes("</br>")
    ) {
        html = html
            .replace(/<br>/g, tempBR)
            .replace(/<br\/>/g, tempBRSlash)
            .replace(/<\/br>/g, tempBRSlash2)
    }
    html = html
        .replace(/&/g, "&amp;") // 转义 &
        .replace(/</g, "&lt;") // 转义 <
        .replace(/>/g, "&gt;") // 转义 >
        .replace(/"/g, "&quot;") // 转义 "
        .replace(/'/g, "&#39;") // 转义 '

    html = html
        .replace(/__TEMP_BR__/g, "<br>")
        .replace(/__TEMP_BR_SLASH__/g, "<br/>")
        .replace(/__TEMP_BR_SLASH2__/g, "</br>")
    return html
}
