<template>
    <div class="core-ui-table-container" :key="refreshQueryParams">
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
        </div>

        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="container-index container"
            :showExpand="false"
            @getRows="getRows"
        >
            <div slot="table" slot-scope="{ data }">
                <common-table :data="data" :columns="columns">
                    <template #外呼问卷="{ row }">
                        <el-button
                            v-if="getBtnInfo(row.id, 'view_callout_record')"
                            type="text"
                            @click="
                                viewQuestionnaire(row, 'view_callout_record')
                            "
                            >查看问卷</el-button
                        >
                    </template>
                    <template #h="{ row }">
                        <el-button type="text" @click="goDetail(row)"
                            >详情</el-button
                        >
                    </template>
                </common-table>
            </div>
        </table-container>

        <qa-dialog
            v-model="isQaDialogVisible"
            :data="qaData"
            @close="onQaDialogClose"
        />
    </div>
</template>

<script lang="ts">
import CommonTable from "@/core-ui/component/common-table/index.vue"
import { TableColumn, TableConfig } from "@/core-ui/component/table"
import TableContainer from "@/core-ui/component/table/container.vue"
import { routesMap } from "@/router/direction"
import { sdk } from "@/service"
import { buildConfig4RemoteMeta } from "@/views/common/list/build-table"
import { BreadcrumbItem } from "@/views/components/breadcrumb"
import {
    getCacheBreadcrumbsByRoutePath,
    updateTagItem,
} from "@/views/pages/single-page/components/tags-view"
import { Component, Vue } from "vue-property-decorator"
import { columns } from "./call-out-detail"
import { cloneDeep } from "lodash"
import QaDialog from "./components/qa.vue"

@Component({
    name: routesMap.collectTaskManage.taskInfoManage.dataVerification
        .callOutDetail,
    components: { TableContainer, CommonTable, QaDialog },
})
export default class DataVerificationDetail extends Vue {
    private breadcrumbs: BreadcrumbItem[] = []

    refreshConfig = {
        fun: this.refresh,
        name: routesMap.collectTaskManage.taskInfoManage.dataVerification
            .callOutDetail,
    }

    private refresh() {
        console.log("refresh")
    }

    private setBreadcrumbs() {
        const d: BreadcrumbItem[] = [
            ...getCacheBreadcrumbsByRoutePath(this.$route.query.from as string),
            {
                label: "外呼详情",
                to: {
                    name: routesMap.collectTaskManage.taskInfoManage
                        .dataVerification.detail,
                    query: {
                        id: this.$route.query.id,
                        from: this.$route.query.from,
                    },
                },
            },
        ]
        updateTagItem({
            name: routesMap.collectTaskManage.taskInfoManage.dataVerification
                .callOutDetail,
            breadcrumb: d,
        })
        this.breadcrumbs = d
    }

    tableConfig: TableConfig | null = null
    private columns: TableColumn[] = columns()

    private async initTable(page: any) {
        try {
            const modelName = page.list.name
            const listName = page.list.list_name

            const config: any = await buildConfig4RemoteMeta(
                modelName,
                listName,
                {
                    useTabs: true,
                    useLabelWidth: true,
                    prefilters: page.list.prefilters.reduce(
                        (acc: any, curr: any) => {
                            acc[curr.property] = curr.value
                            return acc
                        },
                        {}
                    ),
                }
            )

            this.buildConfig(config)
        } catch (error) {
            console.error("创建表格配置失败:", error)
            return {
                tableConfig: {},
                columns: [],
            }
        }
    }

    private buildConfig(r: any) {
        const tableConfig: any = r.tableConfig
        tableConfig.defaultPageSize = 10

        this.tableConfig = null
        this.$nextTick(() => {
            this.tableConfig = tableConfig
        })

        // this.columns = r.columns
    }

    private queryDetail() {
        const detailId = this.$route.query.id as string

        sdk.core
            .model("callout_task")
            .detail2(this.$route.query.id as string, "callout_task_detail")
            .query()
            .then((res) => {
                const {
                    meta: { pages },
                    row,
                } = res

                // 构建第一个tab的配置
                this.initTable(pages[0])
            })
            .catch((error) => {
                console.error("查询详情失败:", error)
            })
    }

    private goDetail(row: any) {
        this.$router.push({
            name: routesMap.collectTaskManage.taskInfoManage.dataVerification
                .personDetail,
            query: {
                model: "callout_record",
                id: row._access_key,
                from: this.$route.name,
            },
        })
    }

    private isQaDialogVisible = false
    private qaData: any = null
    private async viewQuestionnaire(row: any, action_name: string) {
        console.log("row", row)
        console.log("action_name", action_name)
        const action = sdk.core.model("callout_record").action(action_name)
        await action
            .updateInitialParams({
                selected_list: [
                    {
                        id: row.id,
                        v: 0,
                    },
                ],
                prefilters: [
                    {
                        property: "task_id",
                        value: this.$route.query.id,
                    },
                ],
            })
            .query()

        action.execute().then((res: any) => {
            this.qaData =
                typeof res.data === "string"
                    ? JSON.parse(res.data).data
                    : res.data?.data
            console.log(this.qaData)

            this.isQaDialogVisible = true
        })
    }

    private onQaDialogClose() {
        this.qaData = null
        this.isQaDialogVisible = false
    }

    private rows: any[] = []
    private getRows(
        rows: { intents: { name: string }[]; id: { value: string } }[]
    ) {
        const cacheRows = cloneDeep(this.rows)
        const resArr = cacheRows.filter((i) => {
            return !(rows || []).find((j) => j.id.value === i.id.value)
        })
        resArr.push(...(rows || []))
        console.log("获取到的行数据:", resArr)

        this.rows = resArr
    }

    private getBtnInfo(id: string, key: string) {
        const t = this.rows.find((i) => i.id.value === id)
        if (!t) {
            return false
        }
        return (
            (t.actions || []).find((i: any) => i.action_name === key) ||
            (t.intents || []).find((i: any) => i.name === key)
        )
    }

    mounted() {
        this.setBreadcrumbs()
        this.queryDetail()
    }
}
</script>

<style lang="less" scoped></style>
