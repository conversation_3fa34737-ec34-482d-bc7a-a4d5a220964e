<template>
    <div class="content">
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            :useTab="true"
            class="container"
            treeTitle="组织架构"
            @getRows="getRows"
        >
            <div
                slot="title"
                class="d-flex-item-center bold justify-content-between"
            >
                <bread-crumb :backRoute="false" :items="breadcrumbs" />

                <div>
                    <el-button
                        type="primary"
                        v-if="showBtn"
                        @click="addRegionUser"
                        >添加采集员</el-button
                    >
                </div>
            </div>

            <div slot="table" slot-scope="{ data }" class="u-p-20 bg-white">
                <common-table :data="data" :columns="columns">
                    <div
                        slot="h"
                        class="u-flex u-row-center flex-wrap"
                        slot-scope="scope"
                    >
                        <el-button
                            type="text"
                            v-if="
                                getShowBtn4List(
                                    scope.row.id,
                                    'grid_user_service_region_intent_search'
                                )
                            "
                            @click="optServiceRegion(scope.row.id)"
                        >
                            服务区域
                        </el-button>

                        <el-button
                            type="text"
                            v-if="getShowBtn4List(scope.row.id, 'bind_user')"
                            @click="bindUser(scope.row.id)"
                        >
                            绑定用户
                        </el-button>
                        <el-button
                            type="text"
                            v-if="
                                getShowBtn4List(scope.row.id, 'update_status_0')
                            "
                            @click="toOperate(scope.row, 'update_status_0')"
                        >
                            禁用
                        </el-button>

                        <el-button
                            type="text"
                            v-if="
                                getShowBtn4List(scope.row.id, 'update_status_1')
                            "
                            @click="toOperate(scope.row, 'update_status_1')"
                        >
                            启用
                        </el-button>

                        <el-button
                            type="text"
                            v-if="
                                getShowBtn4List(scope.row.id, 'reset_password')
                            "
                            @click="resetPassword(scope.row.id)"
                        >
                            重置密码
                        </el-button>
                    </div>
                </common-table>
            </div>
        </table-container>
        <DialogResetPassword
            v-model="displayResetPassword"
            :curId="curId"
            @refresh="reloadList"
        ></DialogResetPassword>

        <DialogBindUser
            v-model="displayBindUser"
            :curId="curId"
            @refresh="reloadList"
        ></DialogBindUser>

        <DialogServiceRegion
            v-model="displayServiceRegion"
            :curId="curId"
            @refresh="reloadList"
        ></DialogServiceRegion>
    </div>
</template>

<script lang="ts">
    import { TableColumn, TableConfig } from "@/core-ui/component/table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import { Component } from "vue-property-decorator"
    import { Row, predict } from "."
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { routesMap } from "@/router/direction"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { updateTagItem } from "@/views/pages/single-page/components/tags-view"
    import { MessageBox } from "element-ui"
    import { pageLoading } from "@/views/controller"
    import { sdk } from "@/service"
    import { config, EnvProject } from "@/config"
    import {
        buildConfig4RemoteMeta,
        getShowBtn4List,
        getShowBtn4Page,
    } from "@/views/pages/collect-task-manage/components/build-table"
    import DialogResetPassword from "./components/dialog-reset-password.vue"
    import DialogBindUser from "./components/dialog-bind-user.vue"
    import DialogServiceRegion from "./components/dialog-service-region.vue"
    import { cloneDeep } from "lodash"

    @Component({
        name: routesMap.base.regionUserManage.manage,
        components: {
            TableContainer,
            CommonTable,
            DialogResetPassword,
            DialogBindUser,
            DialogServiceRegion,
        },
    })
    export default class CollectInfoManage extends BaseTableController<Row> {
        private isXg = config.envProject === EnvProject.孝感项目

        tableConfig: TableConfig | null = this.getTableConfig()

        refreshConfig = {
            fun: this.reloadList,
            name: routesMap.base.regionUserManage.manage,
        }

        private columns: TableColumn[] = []

        private routesName = routesMap.base.regionUserManage.manage

        private displayResetPassword = false

        private displayBindUser = false

        private displayServiceRegion = false

        breadcrumbs: BreadcrumbItem[] = []
        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                {
                    label: "采集员管理",
                    to: {
                        name: this.routesName,
                    },
                },
            ]
            updateTagItem({
                name: this.routesName,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        private toOperate(row: any, actionName: string) {
            const t = this.getShowBtn4List(row.id, actionName)
            MessageBox.confirm(`确认${t.label}？`, "提示").then(() => {
                pageLoading(() => {
                    return sdk.core
                        .model("grid_user")
                        .action(actionName)
                        .updateInitialParams({
                            selected_list: [{ v: 0, id: row.id }],
                        })
                        .addInputs_parameter({})
                        .execute()
                        .then(() => {
                            this.reloadList()
                        })
                })
            })
        }

        private currentRow = {}

        private showBtn = false

        private rows: any[] = []

        private curId = ""

        mounted() {
            this.setBreadcrumbs()
            this.init()
        }

        private resetPassword(id: string) {
            this.curId = id
            this.displayResetPassword = true
        }

        private bindUser(id: string) {
            this.curId = id
            this.displayBindUser = true
        }

        private getRows(
            rows: { intents: { name: string }[]; id: { value: string } }[]
        ) {
            const cacheRows = cloneDeep(this.rows)
            const resArr = cacheRows.filter((i) => {
                return !(rows || []).find((j) => j.id.value === i.id.value)
            })
            resArr.push(...(rows || []))
            this.rows = resArr
        }

        private getShowBtn4List(id: string, key: string) {
            return getShowBtn4List(this.rows, id, key)
        }

        private init() {
            pageLoading(() => {
                return buildConfig4RemoteMeta("grid_user", "manage", {
                    disabledFilter: true,
                    useLabelWidth: true,
                    useRowFieldGroups: true,
                    optColumn: {
                        label: "操作",
                        prop: "h",
                        fixed: "right",
                        minWidth: "150px",
                    },
                    customLabelWidths: {
                        身份证号: 180,
                        所属区域: 420,
                    },
                }).then((r) => {
                    this.buildConfig(r)
                })
            })
        }

        private buildConfig(r: any) {
            const tableConfig: any = r.tableConfig

            this.showBtn = getShowBtn4Page(r, "insert_unuser")

            if (r.filter && Array.isArray(r.filter)) {
                r.filter.forEach((item: any) => {
                    if (
                        item.prop === "status" ||
                        item.prop === "region_bound_status"
                    ) {
                        if (!item.option) {
                            item.option = {}
                        }
                        item.option.multiple = false
                    }
                })
            }

            tableConfig.filter = r.filter
            tableConfig.predict = {
                ...r.tableConfig.predict,
                actions: "actions",
                intents: "intents",
            }
            this.tableConfig = null

            this.$nextTick(() => {
                this.tableConfig = tableConfig
            })

            this.columns = r.columns
        }

        private getTableConfig(): TableConfig {
            return {
                model: sdk.core.model("grid_user").list("manage"),
                useRemoteFilter: true,
                filter: [],
                defaultPageSize: 10,
                predict,
            }
        }

        private addRegionUser() {
            this.$router.push({
                name: routesMap.base.regionUserManage.manageAdd,
                query: {
                    from: routesMap.base.regionUserManage.manage,
                },
            })
        }

        private optServiceRegion(id: string) {
            this.curId = id
            this.displayServiceRegion = true
        }

        private audit(row: Row, actionName: string, tips: string) {
            this.currentRow = row
            MessageBox.confirm(`确认${tips}？`, "确认").then(() => {
                return pageLoading(() => {
                    return sdk.core
                        .model("grid_user_service_region")
                        .action(actionName)
                        .updateInitialParams({
                            selected_list: [
                                {
                                    id: row.id,
                                    v: 0,
                                },
                            ],
                        })
                        .execute()
                        .then(() => {
                            this.$message.success("修改成功")
                            this.table && this.table.reload(true)
                        })
                })
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";

    .batch-btn {
        width: 120px;
        height: 40px;
        font-size: 14px;
    }
</style>
