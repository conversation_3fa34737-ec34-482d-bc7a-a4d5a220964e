<template>
    <div class="form-filed">
        <VInput
            v-if="type === formType.text"
            :value="value"
            :emptyDisplay="emptyDisplay"
            :isEdit="isEditNow"
            @input="onInput"
            v-bind="option"
            :placeholder="option.placeholder"
        ></VInput>

        <v-input-custom
            v-if="type === formType.textCustom"
            :value="value"
            :emptyDisplay="emptyDisplay"
            :isEdit="isEditNow"
            @input="onInput"
            v-bind="option"
            :placeholder="option.placeholder"
        ></v-input-custom>
        <v-text-range
            v-if="type === formType.textRange"
            :value="value"
            :emptyDisplay="emptyDisplay"
            :isEdit="isEditNow"
            @input="onInput"
            v-bind="option"
            :placeholder="option.placeholder"
        ></v-text-range>
        <v-input-number
            v-if="type === formType.inputNumber"
            :value="value"
            :emptyDisplay="emptyDisplay"
            :isEdit="isEditNow"
            @input="onInput"
            v-bind="option"
            :formItem="formItem"
            :placeholder="option.placeholder"
        ></v-input-number>
        <v-datePicker
            v-if="type === formType.datePicker"
            :value="value"
            :sourceInputsParameter="sourceInputsParameter"
            :subOption="subOption"
            :emptyDisplay="emptyDisplay"
            :isEdit="isEditNow"
            @input="onInput"
            v-bind="option"
            :placeholder="option.placeholder"
        ></v-datePicker>

        <v-select
            v-if="type === formType.select"
            :value="value"
            :sourceInputsParameter="sourceInputsParameter"
            :handleOptions="handleOptions"
            :subOption="subOption"
            :emptyDisplay="emptyDisplay"
            :isEdit="isEditNow"
            @input="onInput"
            v-bind="option"
            :placeholder="option.placeholder"
        ></v-select>
        <v-select2
            v-if="type === formType.select2"
            :value="value"
            :sourceInputsParameter="sourceInputsParameter"
            :handleOptions="handleOptions"
            :subOption="subOption"
            :emptyDisplay="emptyDisplay"
            :isEdit="isEditNow"
            @input="onInput"
            v-bind="option"
            :placeholder="option.placeholder"
        ></v-select2>
        <v-switch
            v-if="type === formType.switch"
            :value="value"
            :sourceInputsParameter="sourceInputsParameter"
            :handleOptions="handleOptions"
            :emptyDisplay="emptyDisplay"
            :isEdit="isEditNow"
            @input="onInput"
            v-bind="option"
            :placeholder="option.placeholder"
        ></v-switch>
        <v-radio
            v-if="type === formType.radio"
            :value="value"
            :sourceInputsParameter="sourceInputsParameter"
            :subOption="subOption"
            :emptyDisplay="emptyDisplay"
            :isEdit="isEditNow"
            @input="onInput"
            v-bind="option"
            :placeholder="option.placeholder"
        ></v-radio>
        <v-checkbox
            v-if="type === formType.checkbox"
            :value="value"
            :sourceInputsParameter="sourceInputsParameter"
            :subOption="subOption"
            :emptyDisplay="emptyDisplay"
            :isEdit="isEditNow"
            @input="onInput"
            v-bind="option"
            :placeholder="option.placeholder"
        ></v-checkbox>
        <v-cascader
            v-if="type === formType.cascader"
            :value="value"
            :sourceInputsParameter="sourceInputsParameter"
            :emptyDisplay="emptyDisplay"
            :isEdit="isEditNow"
            @input="onInput"
            v-bind="option"
            :placeholder="option.placeholder"
        ></v-cascader>
        <v-multiple-cascader
            v-if="type === formType.multipleCascader"
            :value="value"
            :sourceInputsParameter="sourceInputsParameter"
            :emptyDisplay="emptyDisplay"
            :isEdit="isEditNow"
            @input="onInput"
            v-bind="option"
            :placeholder="option.placeholder"
        ></v-multiple-cascader>
        <v-super-cascader
            v-if="type === formType.superCascader"
            :value="value"
            :sourceInputsParameter="sourceInputsParameter"
            :emptyDisplay="emptyDisplay"
            :isEdit="isEditNow"
            @change="onInput"
            v-bind="option"
            :placeholder="option.placeholder"
        ></v-super-cascader>
        <v-file
            v-if="type === formType.file"
            :value="value"
            :emptyDisplay="emptyDisplay"
            :isEdit="isEditNow"
            @input="onInput"
            v-bind="option"
            :placeholder="option.placeholder"
        ></v-file>
        <v-img-upload
            v-if="type === formType.imgUpload"
            :value="value"
            :emptyDisplay="emptyDisplay"
            :isEdit="isEditNow"
            @input="onInput"
            v-bind="option"
            :placeholder="option.placeholder"
        ></v-img-upload>
        <MyUpload
            v-if="type === formType.myUpload"
            :value="value"
            :emptyDisplay="emptyDisplay"
            :isEdit="isEditNow"
            :prop="formItem.prop"
            @input="onInput"
            v-bind="option"
            :placeholder="option.placeholder"
        >
            <div v-if="$slots[formItem.prop]">
                <slot :name="formItem.prop" />
            </div>
        </MyUpload>
        <v-tip v-if="type === formType.tip" :formItem="formItem"></v-tip>
        <v-intent-search
            v-if="type === formType.intentSearch"
            :value="value"
            :sourceInputsParameter="sourceInputsParameter"
            @changeSourceInputsParameter="
                $emit('changeSourceInputsParameter', $event)
            "
            :handleOptions="handleOptions"
            :emptyDisplay="emptyDisplay"
            :isEdit="isEditNow"
            @input="onInput"
            v-bind="option"
            :placeholder="option.placeholder"
            @changeRow="
                $emit('changeRow', {
                    data: $event,
                    prop:
                        (formItem && formItem.prop) ||
                        sourceInputsParameter.property,
                })
            "
        ></v-intent-search>
        <v-intent-search
            v-if="type === formType.multi_intentSearch"
            :value="value"
            :sourceInputsParameter="sourceInputsParameter"
            @changeSourceInputsParameter="
                $emit('changeSourceInputsParameter', $event)
            "
            :handleOptions="handleOptions"
            :emptyDisplay="emptyDisplay"
            :isEdit="isEditNow"
            @input="onInput"
            v-bind="option"
            :placeholder="option.placeholder"
            @changeRow="
                $emit('changeRow', {
                    data: $event,
                    prop:
                        (formItem && formItem.prop) ||
                        sourceInputsParameter.property,
                })
            "
            :multiple="true"
        ></v-intent-search>
        <v-intent-search-remote
            v-if="type === formType.intentSearchRemote"
            :value="value"
            :sourceInputsParameter="sourceInputsParameter"
            @changeSourceInputsParameter="
                $emit('changeSourceInputsParameter', $event)
            "
            :handleOptions="handleOptions"
            :emptyDisplay="emptyDisplay"
            :isEdit="isEditNow"
            @input="onInput"
            v-bind="option"
            :intentSearchConfig="option.intentSearchConfig || {}"
            :placeholder="option.placeholder"
            @changeRow="
                $emit('changeRow', {
                    data: $event,
                    prop:
                        (formItem && formItem.prop) ||
                        sourceInputsParameter.property,
                })
            "
        ></v-intent-search-remote>
        <v-intent-search-remote
            v-if="type === formType.multi_intentSearchRemote"
            :value="value"
            :sourceInputsParameter="sourceInputsParameter"
            @changeSourceInputsParameter="
                $emit('changeSourceInputsParameter', $event)
            "
            :handleOptions="handleOptions"
            :emptyDisplay="emptyDisplay"
            :isEdit="isEditNow"
            @input="onInput"
            v-bind="option"
            :intentSearchConfig="option.intentSearchConfig || {}"
            :placeholder="option.placeholder"
            @changeRow="
                $emit('changeRow', {
                    data: $event,
                    prop:
                        (formItem && formItem.prop) ||
                        sourceInputsParameter.property,
                })
            "
            :multiple="true"
        ></v-intent-search-remote>
        <v-tree
            v-if="type === formType.tree"
            :value="value"
            :sourceInputsParameter="sourceInputsParameter"
            @changeSourceInputsParameter="
                $emit('changeSourceInputsParameter', $event)
            "
            :emptyDisplay="emptyDisplay"
            :isEdit="isEditNow"
            @input="onInput"
            v-bind="option"
            :placeholder="option.placeholder"
            :inFilter="inFilter"
        ></v-tree>
        <v-tree-2
            v-if="type === formType.tree2"
            :value="value"
            :sourceInputsParameter="sourceInputsParameter"
            @changeSourceInputsParameter="
                $emit('changeSourceInputsParameter', $event)
            "
            :emptyDisplay="emptyDisplay"
            :isEdit="isEditNow"
            :treeConfig="option.treeConfig || null"
            @input="onInput"
            v-bind="option"
            :placeholder="option.placeholder"
            :inFilter="inFilter"
        ></v-tree-2>
        <v-detail-list
            :isEdit="isEditNow"
            :formItem="formItem"
            @input="onInput"
            v-bind="option"
            :placeholder="option.placeholder"
            v-if="type === formType.detailList"
        ></v-detail-list>
        <v-employee-select
            :value="value"
            :meta="sourceInputsParameter.ext_properties.employeeSelect"
            :sourceInputsParameter="sourceInputsParameter"
            @input="onInput"
            v-bind="option"
            :placeholder="option.placeholder"
            v-if="type === formType.employeeSelect"
        ></v-employee-select>
    </div>
</template>

<script lang="ts">
    import { Component, Prop } from "vue-property-decorator"
    import { FormItemOption, FormType } from ".."
    import { BaseFiled } from "./base-filed"
    import VTree from "./tree/index.vue"
    import VTree2 from "./tree2/index.vue"
    import VIntentSearch from "./intent-search/index.vue"
    import VIntentSearchRemote from "./intent-search-remote/index.vue"
    import MyUpload from "./my-upload/index.vue"
    import VCascader from "./v-cascader.vue"
    import VDatePicker from "./v-date-picker.vue"
    import VFile from "./v-file.vue"
    import VInputCustom from "./v-input-custom.vue"
    import VImgUpload from "./v-img-upload.vue"
    import VMultipleCascader from "./v-multiple-cascader.vue"
    import VRadio from "./v-radio.vue"
    import VCheckbox from "./v-checkbox.vue"
    import VSelect from "./v-select.vue"
    import VSelect2 from "./v-select2.vue"
    import VSuperCascader from "./v-super-cascader.vue"
    import VSwitch from "./v-switch.vue"
    import VTextRange from "./v-text-range.vue"
    import VTip from "./v-tip.vue"
    import { Input } from "@/core-ui/component/form"
    import VInputNumber from "./v-input-number.vue"
    import VDetailList from "./detail-list/v-detail-list.vue"
    import VEmployeeSelect from "./employee-select/index.vue"
    import VInput from "./v-input/index.vue"

    @Component({
        name: "filed",
        components: {
            VInput,
            VSelect,
            VSelect2,
            VRadio,
            VCheckbox,
            VTree,
            VTree2,
            VMultipleCascader,
            VCascader,
            VFile,
            VInputCustom,
            VDatePicker,
            VTextRange,
            VSwitch,
            VImgUpload,
            VTip,
            MyUpload,
            VSuperCascader,
            VIntentSearch,
            VIntentSearchRemote,
            VInputNumber,
            VDetailList,
            VEmployeeSelect,
        },
    })
    export default class Filed extends BaseFiled {
        @Prop()
        protected readonly type!: FormType

        @Prop()
        protected readonly option?: FormItemOption

        @Prop({ default: false })
        inFilter!: boolean
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .form-filed {
        ::v-deep .el-select,
        ::v-deep .el-range-editor,
        ::v-deep .el-date-editor,
        ::v-deep .el-cascader,
        ::v-deep .el-autocomplete,
        ::v-deep .el-input-number {
            width: 100%;
        }
    }
    .text-display {
        white-space: pre-line;
    }
</style>
