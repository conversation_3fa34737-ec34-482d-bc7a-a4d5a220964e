<template>
    <el-dialog
        append-to-body
        :visible="value"
        @close="close"
        title="导出申请"
        width="600px"
    >
        <form-builder ref="formBuilder" labelWidth="90px"></form-builder>
        <div class="u-flex u-row-center">
            <el-button type="primary" plain @click="close">取消</el-button>
            <el-button type="primary" class="u-m-l-30" @click="confirm"
                >确定</el-button
            >
        </div>
    </el-dialog>
</template>

<script lang='ts'>
    import {
        BuildFormConfig,
        buildFormSections,
        FormController,
        FormType,
    } from "@/core-ui/component/form"
    import FormBuilder from "@/core-ui/component/form/index.vue"
    import { DialogController } from "@/core-ui/controller/dialog-controller"
    import { sdk } from "@/service"
    import { pageLoading } from "@/views/controller"
    import { Component, Mixins, Prop } from "vue-property-decorator"

    function buildConfig(
        model_name: string,
        data_type: string,
        parameters: string
    ): BuildFormConfig {
        return {
            sdkModel: "export_task_manage",
            sdkAction: "insert",
            forms: [
                {
                    label: "model_name",
                    type: FormType.Text,
                    prop: "model_name",
                    hide: true,
                    defaultValue: model_name,
                },
                {
                    label: "任务名称",
                    type: FormType.Text,
                    prop: "task_name",
                },
                {
                    label: "数据类型",
                    type: FormType.Text,
                    prop: "data_type",
                    hide: true,
                    defaultValue: data_type,
                },
                {
                    label: "查询参数",
                    type: FormType.Text,
                    prop: "parameters",
                    hide: true,
                    defaultValue: parameters,
                },
            ],
        }
    }

    @Component({ components: { FormBuilder } })
    export default class ExportApplyPop extends Mixins(
        DialogController,
        FormController
    ) {
        @Prop()
        private exportFormsValue!: {
            model_name: string
            data_type: string
            parameters: string
        }

        onOpen() {
            this.init()
        }

        private init() {
            return buildFormSections(
                buildConfig(
                    this.exportFormsValue.model_name,
                    this.exportFormsValue.data_type,
                    this.exportFormsValue.parameters
                )
            ).then((r) => {
                console.log(6666, r, this.exportFormsValue)

                this.buildFormFull(r)
            })
        }

        private confirm() {
            const data = this.getFormValues()
            this.validateForm((v: boolean) => {
                if (v) {
                    this.submit(data)
                }
            })
        }

        private submit(data: any) {
            return pageLoading(() => {
                return sdk.core
                    .model("export_task_manage")
                    .action("insert")
                    .addInputs_parameter(data)
                    .execute()
                    .then(() => {
                        this.$message.success("提交成功")
                        this.close()
                    })
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
</style>
