<template>
    <div class="detail-top-box">
        <div class="detail-top-header">
            <div class="detail-top-title">{{ title }}</div>
        </div>
        <el-collapse-transition>
            <div v-show="isExpanded">
                <detail-row-col
                    :labelStyle="labelStyle"
                    :list="items"
                    class="u-p-x-20 u-p-t-20"
                >
                </detail-row-col>
            </div>
        </el-collapse-transition>
        <div class="expand-container">
            <span class="expand-btn" @click="toggleExpand">
                {{ isExpanded ? "收起" : "展开" }}
                <i
                    :class="[
                        'el-icon-arrow-down',
                        { 'expand-btn-arrow-up': isExpanded },
                    ]"
                ></i>
            </span>
        </div>
    </div>
</template>

<script lang="ts">
import { ColItem } from "@/views/components/detail-row-col"
import { Component, Prop, Vue } from "vue-property-decorator"
import DetailRowCol from "@/views/components/detail-row-col/index.vue"

@Component({
    components: {
        DetailRowCol,
    },
})
export default class TemplateView extends Vue {
    @Prop({})
    private items!: ColItem[]

    @Prop({})
    private title!: string

    private isExpanded = false

    private toggleExpand() {
        this.isExpanded = !this.isExpanded
    }

    private get labelStyle() {
        return {
            minWidth: "78px",
            textAlign: "left",
            color: "#555",
        }
    }
}
</script>

<style lang="less" scoped>
@import "~@/css/variables.less";
@import "~@/css/table-container.less";

.detail-top-header {
    padding-left: 20px;
    padding-right: 20px;
    display: flex;
    align-items: center;
    width: 100%;
}

.detail-top-title {
    font-weight: 600;
    font-size: 18px;
    color: #222222;
    line-height: 18px;
}

.detail-top-box {
    background-color: #fff;
    margin-bottom: 20px;
    padding-top: 20px;
    width: 100%;
    padding-bottom: 20px;
    position: relative;
}

.expand-container {
    position: absolute;
    bottom: 0;
    right: 0;
    left: 0;
    margin: auto;
    text-align: center;
}

.expand-btn {
    cursor: pointer;
    color: rgba(116, 126, 178, 1);
    background-color: rgba(255, 255, 255, 1);
    height: 25px;
    width: 68px;
    line-height: 25px;
    border-radius: 3px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transform: translateY(50%);

    .expand-btn-arrow-up {
        transform: rotate(180deg);
    }

    i {
        transition: transform 0.3s;
        margin-left: 5px;
    }
}
</style>
